import { createTransport, TransportOptions } from 'nodemailer';
import config from '../config';
import { logSecurity } from './logger';
import { LogEventType } from '../types/logger';
import dotenv from 'dotenv';
import { formatTicketStatus } from '../utils/formatters';
import logger from '../utils/logger';
import { sendModerationActionEmail, sendEntrepriseVerificationStatus } from './emailServiceModeration';
import { queueEmail } from './emailQueueService';
import { redis } from '../config/redis';
import { sendAiCreditsEmail } from './emailAiCredits';

dotenv.config();

// Validation de la configuration SMTP
const validateSmtpConfig = (config: any) => {
  const missingConfig = [];
  const requiredFields = ['host', 'port', 'auth.user', 'auth.pass'];

  for (const field of requiredFields) {
    const value = field.split('.').reduce((obj, key) => obj?.[key], config);
    if (!value) {
      missingConfig.push(field);
    }
  }

  if (missingConfig.length > 0) {
    throw new EmailServiceError(
      EMAIL_ERRORS.CONFIGURATION_ERROR.message(missingConfig),
      EMAIL_ERRORS.CONFIGURATION_ERROR.code,
      { missingConfig }
    );
  }

  return true;
};

// Configuration du transporteur SMTP
const transporter = createTransport(config.smtp as TransportOptions);

// Vérification de la configuration
if (!validateSmtpConfig(config.smtp)) {
  logSecurity.error(LogEventType.EMAIL_ERROR, 'Configuration SMTP incomplète');
}

// Test de connexion SMTP
export const testSmtpConnection = async () => {
  try {
    logSecurity.info(LogEventType.EMAIL_VERIFY, 'Test de la connexion SMTP', {
      smtpHost: config.smtp.host || 'non défini',
      smtpPort: config.smtp.port || 'non défini',
      smtpUser: config.smtp.auth.user || 'non défini',
      smtpSecure: config.smtp.secure ? 'oui' : 'non'
    });

    // Utilisation du transporteur global déjà créé
    await transporter.verify();

    logSecurity.info(LogEventType.EMAIL_SENT, 'Connexion SMTP établie avec succès');
    return true;
  } catch (error) {
    logSecurity.error(LogEventType.EMAIL_ERROR, 'Échec du test de connexion SMTP:', error instanceof Error ? error : new Error(String(error)));
    throw error;
  }
};

// Configuration du rate limiting
const rateLimitConfig = {
  maxEmails: Number(process.env.EMAIL_RATE_LIMIT || 50), // 50 emails par heure
  windowMs: Number(process.env.EMAIL_RATE_WINDOW || 3600) * 1000, // pour 1 heure
};

// Configuration des retries
const retryConfig = {
  attempts: Number(process.env.EMAIL_RETRY_ATTEMPTS || 5),
  delayMs: Number(process.env.EMAIL_RETRY_DELAY || 5000),
};

// Map pour stocker le nombre d'emails envoyés par utilisateur
const emailCountMap = new Map<string, { count: number; resetTime: number }>();

// Classe d'erreur personnalisée pour les emails
export class EmailServiceError extends Error {
  constructor(
    message: string,
    public readonly code: string,
    public readonly details?: any
  ) {
    super(message);
    this.name = 'EmailServiceError';
  }
}

type EmailIcons = {
  [K in keyof typeof EMAIL_SUBJECTS]: string;
};

const EMAIL_ICONS: EmailIcons = {
  emailVerification: '🚀',
  passwordReset: '🔐',
  loginAttemptWarning: '⚠️',
  accountLocked: '🚫',
  welcomeEmail: '🌟',
  profilActivation: '🌟',
  securityAlert: '⚠️',
  passwordExpiration: '⏰',
  newLogin: '🔔',
  suspensionEmail: '🚫',
  photosDeletion: '📸',
  newMessage: '💬',
  newConversation: '👋',
  reviewReceived: '⭐',
  reviewPosted: '📝',
  reviewReply: '💬',
  reviewDeleted: '🗑',
  newMission: '🔔',
  missionAutoClosure: '🔒',
  newProposal: '💼',
  counterOffer: '💰',
  proposalAccepted: '✅',
  proposalRejected: '❌',
  jobbeurCounterOffer: '💰',
  contactInfo: '📞',
  missionCancelled: '❌',
  offerExpiration: '⏰',
  offerExpirationReminder: '⏰',
  referralWelcome: '🎁',
  referrerNotification: '🎉',
  referralReward: '💰',
  newBugReport: '🐛',
  bugReportComment: '💬',
  accountDeletion: '🗑️',
  bugReportStatusChange: '🔄',
  emailChangeNotification: '📧',
  emailChangeConfirmation: '✅',
  newSupportTicket: '🎫',
  supportTicketComment: '💬',
  supportTicketStatusChange: '🔄',
  supportTicketAssigned: '👤',
  jobiExchangeConfirmation: '💸',
  jobiExchangeReceived: '💰',
  paymentReceived: '💸',
  paymentConfirmed: '💰',
  new_badges: '🏆',
  invoice_email: '📄',
  subscription_status_change: '📝',
  twoFactorAuth: '🔒',
  twoFactorEnabled: '🔐',
  twoFactorDisabled: '🔓',
  aiCredits: '🤖'
};

const EMAIL_SUBJECTS = {
  emailVerification: 'Votre compte vous attend : vérifiez votre adresse email.',
  passwordReset: 'Réinitialisation de votre mot de passe',
  loginAttemptWarning: 'Tentative de connexion suspecte',
  accountLocked: 'Votre compte a été verrouillé',
  welcomeEmail: 'Découvrez votre nouvelle plateforme : bienvenue à bord !',
  profilActivation: 'Activez votre profil et soyez visible dès aujourd\'hui !',
  securityAlert: 'Alerte : une activité suspecte détectée sur votre compte',
  passwordExpiration: 'Votre mot de passe arrive à expiration : mettez-le à jour',
  newLogin: 'Une nouvelle connexion a été détectée sur votre compte.',
  suspensionEmail: 'Votre compte a été suspendu',
  photosDeletion: 'Suppression des photos de vos missions',
  newMessage: 'Nouveau message sur JobPartiel',
  newConversation: 'Nouvelle conversation sur JobPartiel',
  reviewReceived: 'Nouvel avis reçu sur votre profil',
  reviewPosted: 'Votre avis a été publié avec succès',
  reviewReply: 'Réponse à votre avis',
  reviewDeleted: 'Un avis a été supprimé',
  newMission: 'Nouvelle mission disponible',
  missionAutoClosure: 'Mission fermée automatiquement',
  newProposal: 'Nouvelle proposition reçue',
  counterOffer: 'Contre-offre reçue',
  proposalAccepted: 'Proposition acceptée',
  proposalRejected: 'Proposition refusée',
  jobbeurCounterOffer: 'Nouvelle contre-offre',
  contactInfo: 'Informations de contact partagées',
  missionCancelled: 'Mission annulée',
  offerExpiration: 'Offre expirée',
  offerExpirationReminder: 'Rappel : offre en attente d\'expiration',
  referralWelcome: 'Bienvenue grâce à un parrainage',
  referrerNotification: 'Nouveau filleul',
  referralReward: 'Récompense de parrainage',
  newBugReport: 'Nouveau rapport de bug',
  bugReportComment: 'Nouveau commentaire sur le rapport',
  accountDeletion: 'Confirmation de suppression de votre compte JobPartiel',
  bugReportStatusChange: 'Statut du rapport modifié',
  emailChangeNotification: 'Modification d\'adresse email',
  emailChangeConfirmation: 'Email modifié avec succès',
  newSupportTicket: 'Nouveau ticket de support',
  supportTicketComment: 'Nouveau commentaire sur le ticket',
  supportTicketStatusChange: 'Statut du ticket modifié',
  supportTicketAssigned: 'Ticket assigné',
  jobiExchangeConfirmation: 'Confirmation d\'envoi de Jobi',
  jobiExchangeReceived: 'Réception de Jobi',
  paymentReceived: 'Paiement reçu pour votre mission',
  paymentConfirmed: 'Confirmation de paiement',
  new_badges: 'Nouveaux badges obtenus',
  invoice_email: 'Facture/Devis',
  subscription_status_change: 'Changement de statut de votre abonnement',
  twoFactorAuth: 'Code de vérification pour votre connexion',
  twoFactorEnabled: 'Authentification à deux facteurs activée',
  twoFactorDisabled: 'Authentification à deux facteurs désactivée',
  aiCredits: 'Confirmation d\'achat de crédits IA'
};

// Messages d'erreur constants
const EMAIL_ERRORS = {
  RATE_LIMIT: {
    code: 'EMAIL_RATE_LIMIT_EXCEEDED',
    message: (email: string, maxEmails: number, windowMs: number) =>
      `Limite de ${maxEmails} emails par ${windowMs / 1000} secondes dépassée pour ${email}. Veuillez réessayer plus tard.`
  },
  SMTP_ERROR: {
    code: 'SMTP_ERROR',
    message: (details: string) =>
      `Erreur lors de la communication avec le serveur SMTP : ${details}`
  },
  RETRY_FAILED: {
    code: 'MAX_RETRIES_EXCEEDED',
    message: (attempts: number) =>
      `L'envoi de l'email a échoué après ${attempts} tentatives. Veuillez réessayer plus tard.`
  },
  INVALID_EMAIL: {
    code: 'INVALID_EMAIL',
    message: (email: string) =>
      `L'adresse email '${email}' n'est pas valide.`
  },
  TEMPLATE_ERROR: {
    code: 'TEMPLATE_ERROR',
    message: (templateName: string) =>
      `Erreur lors du chargement du template d'email '${templateName}'.`
  },
  MISSING_REQUIRED_FIELDS: {
    code: 'MISSING_REQUIRED_FIELDS',
    message: (fields: string[]) =>
      `Champs obligatoires manquants : ${fields.join(', ')}`
  },
  CONFIGURATION_ERROR: {
    code: 'CONFIGURATION_ERROR',
    message: (missingConfig: string[]) =>
      `Configuration SMTP incomplète. Variables manquantes : ${missingConfig.join(', ')}`
  },
  BLACKLISTED_EMAIL: {
    code: 'BLACKLISTED_EMAIL',
    message: (email: string) =>
      `L'adresse email '${email}' est blacklistée ou a été signalée comme spam.`
  },
  ATTACHMENT_ERROR: {
    code: 'ATTACHMENT_ERROR',
    message: (details: string) =>
      `Erreur avec la pièce jointe : ${details}`
  },
  CONNECTION_ERROR: {
    code: 'CONNECTION_ERROR',
    message: (host: string) =>
      `Impossible de se connecter au serveur SMTP (${host}). Vérifiez votre connexion internet.`
  },
  AUTHENTICATION_ERROR: {
    code: 'AUTHENTICATION_ERROR',
    message: () =>
      `Échec de l'authentification SMTP. Vérifiez vos identifiants.`
  },
  SENDING_ERROR: {
    code: 'SENDING_ERROR',
    message: (error: string) =>
      `Erreur lors de l'envoi de l'email : ${error}`
  }
};

// Fonction pour valider le format de l'email
const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  
  // Vérifier le format de base
  if (!emailRegex.test(email)) {
    return false;
  }
  
  // Exclure les emails anonymisés pour le RGPD
  if (email.includes('@supprime.local')) {
    return false;
  }
  
  return true;
};

// Fonction pour vérifier le rate limiting
const checkRateLimit = (email: string): boolean => {
  const now = Date.now();
  const userEmailCount = emailCountMap.get(email);

  if (!userEmailCount || now > userEmailCount.resetTime) {
    emailCountMap.set(email, { count: 1, resetTime: now + rateLimitConfig.windowMs });
    return true;
  }

  if (userEmailCount.count >= rateLimitConfig.maxEmails) {
    throw new EmailServiceError(
      EMAIL_ERRORS.RATE_LIMIT.message(email, rateLimitConfig.maxEmails, rateLimitConfig.windowMs),
      EMAIL_ERRORS.RATE_LIMIT.code,
      { email, count: userEmailCount.count, resetTime: userEmailCount.resetTime }
    );
  }

  userEmailCount.count += 1;
  return true;
};

// Fonction pour envoyer un email avec retry
export const sendEmailWithRetry = async (mailOptions: any): Promise<void> => {
  if (!isValidEmail(mailOptions.to)) {
    logSecurity.error(LogEventType.EMAIL_ERROR, 'Adresse email invalide', { email: mailOptions.to });
    throw new EmailServiceError(
      EMAIL_ERRORS.INVALID_EMAIL.message(mailOptions.to),
      EMAIL_ERRORS.INVALID_EMAIL.code,
      { email: mailOptions.to }
    );
  }

  for (let attempt = 1; attempt <= retryConfig.attempts; attempt++) {
    try {
      await transporter.sendMail(mailOptions);
      logSecurity.info(LogEventType.EMAIL_SENT, 'Email envoyé avec succès', {
        to: mailOptions.to,
        subject: mailOptions.subject
      });
      return;
    } catch (error: any) {
      logSecurity.error(LogEventType.EMAIL_ERROR, `Tentative ${attempt}/${retryConfig.attempts} échouée`, {
        error: error.message,
        attempt,
        maxAttempts: retryConfig.attempts
      });

      if (attempt === retryConfig.attempts) {
        throw new EmailServiceError(
          EMAIL_ERRORS.RETRY_FAILED.message(retryConfig.attempts),
          EMAIL_ERRORS.RETRY_FAILED.code,
          { attempts: retryConfig.attempts, lastError: error.message }
        );
      }

      logSecurity.info(LogEventType.EMAIL_ERROR, `Attente avant prochaine tentative`, {
        delayMs: retryConfig.delayMs,
        nextAttempt: attempt + 1
      });
      await new Promise(resolve => setTimeout(resolve, retryConfig.delayMs));
    }
  }
};

export const sendVerificationEmail = async (email: string, tokenData: { token: string, verificationLink?: string }) => {
  try {
    // Vérification du rate limit
    checkRateLimit(email);

    const verificationLink = tokenData.verificationLink ||
      `${process.env.FRONTEND_URL}/verify-email?token=${tokenData.token}`;

    const mailOptions = {
      from: `"Job Partiel" <${process.env.SMTP_FROM}>` || '<EMAIL>',
      to: email,
      subject: `${EMAIL_ICONS.emailVerification} ${EMAIL_SUBJECTS.emailVerification}`,
      html: `
      <div style="font-family: 'Arial', sans-serif; width: 100%; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #FFF8F3 0%, #FFE4BA 100%); padding: 40px 0;">
        <div style="background-color: white; padding: 40px 20px; border-radius: 16px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
          <div style="text-align: center; margin-bottom: 30px;">
            <img src="${config.apiUrl}/api/storage-proxy/photo_profil/avatar/avatar-defaut-jobpartiel.jpg" alt="Job Partiel" style="max-width: 100%; height: auto;">
          </div>

          <h2 style="color: #FF7A35; text-align: center; font-size: 28px; margin-bottom: 25px;">Bienvenue sur Job Partiel !</h2>

          <p style="line-height: 1.8; color: #374151; font-size: 16px;">
            Bonjour,
          </p>

          <p style="font-size: 16px; margin-top: 15px;">
            Nous sommes ravis de vous accueillir sur Job Partiel ! Pour finaliser votre inscription et sécuriser votre compte, merci de cliquer sur le bouton de vérification ci-dessous.
          </p>

          <div style="text-align: center; margin: 25px 0;">
            <a href="${verificationLink}" style="
              display: inline-block;
              background-color: #FF7A35;
              color: white;
              padding: 14px 32px;
              text-decoration: none;
              border-radius: 8px;
              font-weight: 600;
              font-size: 16px;
              transition: all 0.3s ease;
              box-shadow: 0 2px 4px rgba(255, 122, 53, 0.2);
            ">Vérifier mon email</a>
          </div>

          <div style="
            margin: 20px auto;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 8px;
            max-width: 90%;
          ">
            <p style="
              color: #6b7280;
              font-size: 14px;
              margin-bottom: 10px;
              text-align: center;
            ">Si le bouton ne fonctionne pas, copiez et collez ce lien dans votre navigateur :</p>
            <p style="
              background-color: white;
              padding: 12px;
              border-radius: 6px;
              border: 1px solid #e5e7eb;
              font-family: monospace;
              font-size: 13px;
              color: #374151;
              word-break: break-all;
              margin: 0;
              text-align: center;
            ">${verificationLink}</p>
          </div>


          <p style="font-size: 14px; color: #666; margin-top: 20px;">
            Si vous n'avez pas créé de compte sur Job Partiel, vous pouvez ignorer cet email.
          </p>

          <div style="border-top: 1px solid #e0e0e0; margin-top: 20px; padding-top: 15px; text-align: center;">
            <p style="font-size: 12px; color: #888;">
              Ce lien de vérification expirera dans 24 heures.
            </p>
          </div>
        </div>

        <div style="text-align: center; margin-top: 15px; color: #666; font-size: 12px;">
          ${new Date().getFullYear()} Job Partiel. Tous droits réservés.
        </div>
      </div>
      <style>
        @media only screen and (max-width: 600px) {
          h2 { font-size: 24px; }
          h3 { font-size: 18px; }
          h4 { font-size: 16px; }
          p, ul { font-size: 14px; }
        }
      </style>
    `
    };

    logSecurity.info(LogEventType.EMAIL_VERIFY, 'Envoi de l\'email de vérification', {
      smtpHost: config.smtp.host,
      smtpPort: config.smtp.port,
      smtpUser: config.smtp.auth.user,
      smtpSecure: config.smtp.secure
    });

    await queueEmail(mailOptions.to, mailOptions.subject, mailOptions.html);
    logSecurity.info(LogEventType.EMAIL_SENT, 'Email de vérification envoyé avec succès', { to: email });
  } catch (error) {
    if (error instanceof EmailServiceError) {
      throw error;
    }

    const errorMessage = error instanceof Error ? error.message : 'Une erreur inconnue est survenue';
    throw new EmailServiceError(
      EMAIL_ERRORS.SMTP_ERROR.message(errorMessage),
      EMAIL_ERRORS.SMTP_ERROR.code,
      { originalError: errorMessage }
    );
  }
};

export const sendTwoFactorAuthEmail = async (email: string, tokenData: { token: string, twoFactorLink?: string }) => {
  try {
    // Vérification du rate limit
    checkRateLimit(email);

    const twoFactorLink = tokenData.twoFactorLink ||
      `${process.env.FRONTEND_URL}/verify-two-factor?token=${tokenData.token}`;

    const mailOptions = {
      from: `"Job Partiel" <${process.env.SMTP_FROM}>` || '<EMAIL>',
      to: email,
      subject: `${EMAIL_ICONS.twoFactorAuth} ${EMAIL_SUBJECTS.twoFactorAuth}`,
      html: `
      <div style="font-family: 'Arial', sans-serif; width: 100%; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #FFF8F3 0%, #FFE4BA 100%); padding: 40px 0;">
        <div style="background-color: white; padding: 40px 20px; border-radius: 16px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
          <div style="text-align: center; margin-bottom: 30px;">
            <img src="${config.apiUrl}/api/storage-proxy/photo_profil/avatar/avatar-defaut-jobpartiel.jpg" alt="Job Partiel" style="max-width: 100%; height: auto;">
          </div>

          <h2 style="color: #FF7A35; text-align: center; font-size: 28px; margin-bottom: 25px;">Code de vérification</h2>

          <p style="line-height: 1.8; color: #374151; font-size: 16px;">
            Bonjour,
          </p>

          <p style="font-size: 16px; margin-top: 15px;">
            Vous avez demandé à vous connecter à votre compte Job Partiel. Pour finaliser votre connexion, veuillez utiliser le code de vérification ci-dessous :
          </p>

          <div style="text-align: center; margin: 25px 0;">
            <div style="
              display: inline-block;
              background-color: #f8f9fa;
              padding: 15px 30px;
              border-radius: 8px;
              font-size: 32px;
              font-weight: bold;
              letter-spacing: 5px;
              color: #FF7A35;
              border: 2px solid #FFE4BA;
            ">${tokenData.token}</div>
          </div>

          <p style="font-size: 16px; margin-top: 15px; text-align: center;">
            Ou cliquez sur le bouton ci-dessous pour vous connecter :
          </p>

          <div style="text-align: center; margin: 25px 0;">
            <a href="${twoFactorLink}" style="
              display: inline-block;
              background-color: #FF7A35;
              color: white;
              padding: 14px 32px;
              text-decoration: none;
              border-radius: 8px;
              font-weight: 600;
              font-size: 16px;
              transition: all 0.3s ease;
              box-shadow: 0 2px 4px rgba(255, 122, 53, 0.2);
            ">Vérifier mon identité</a>
          </div>

          <div style="
            margin: 20px auto;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 8px;
            max-width: 90%;
          ">
            <p style="
              color: #6b7280;
              font-size: 14px;
              margin-bottom: 10px;
              text-align: center;
            ">Si le bouton ne fonctionne pas, copiez et collez ce lien dans votre navigateur :</p>
            <p style="
              background-color: white;
              padding: 12px;
              border-radius: 6px;
              border: 1px solid #e5e7eb;
              font-family: monospace;
              font-size: 13px;
              color: #374151;
              word-break: break-all;
              margin: 0;
              text-align: center;
            ">${twoFactorLink}</p>
          </div>

          <p style="font-size: 14px; color: #666; margin-top: 20px;">
            Si vous n'avez pas tenté de vous connecter à votre compte Job Partiel, veuillez ignorer cet email et sécuriser votre compte en changeant votre mot de passe.
          </p>

          <div style="border-top: 1px solid #e0e0e0; margin-top: 20px; padding-top: 15px; text-align: center;">
            <p style="font-size: 12px; color: #888;">
              Ce code de vérification expirera dans 10 minutes.
            </p>
          </div>
        </div>

        <div style="text-align: center; margin-top: 15px; color: #666; font-size: 12px;">
          ${new Date().getFullYear()} Job Partiel. Tous droits réservés.
        </div>
      </div>
      <style>
        @media only screen and (max-width: 600px) {
          h2 { font-size: 24px; }
          h3 { font-size: 18px; }
          h4 { font-size: 16px; }
          p, ul { font-size: 14px; }
        }
      </style>
    `
    };

    logSecurity.info(LogEventType.EMAIL_VERIFY, 'Envoi de l\'email d\'authentification à deux facteurs', {
      smtpHost: config.smtp.host,
      smtpPort: config.smtp.port,
      smtpUser: config.smtp.auth.user,
      smtpSecure: config.smtp.secure
    });

    await queueEmail(mailOptions.to, mailOptions.subject, mailOptions.html);
    logSecurity.info(LogEventType.EMAIL_SENT, 'Email d\'authentification à deux facteurs envoyé avec succès', { to: email });
  } catch (error) {
    if (error instanceof EmailServiceError) {
      throw error;
    }

    const errorMessage = error instanceof Error ? error.message : 'Une erreur inconnue est survenue';
    throw new EmailServiceError(
      EMAIL_ERRORS.SMTP_ERROR.message(errorMessage),
      EMAIL_ERRORS.SMTP_ERROR.code,
      { originalError: errorMessage }
    );
  }
};

export const sendPasswordResetEmail = async (email: string, resetToken: string) => {
  try {
    // Vérification du rate limit
    checkRateLimit(email);

    // Construire le lien de réinitialisation complet
    const resetLink = `${process.env.FRONTEND_URL}/reset-password?token=${resetToken}`;

    const mailOptions = {
      from: `"Job Partiel" <${process.env.SMTP_FROM}>` || '<EMAIL>',
      to: email,
      subject: `${EMAIL_ICONS.passwordReset} ${EMAIL_SUBJECTS.passwordReset}`,
      html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #FFF8F3 0%, #FFE4BA 100%); padding: 40px 0;">
        <div style="background-color: white; padding: 40px 20px; border-radius: 16px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
          <div style="text-align: center; margin-bottom: 30px;">
            <img src="${config.apiUrl}/api/storage-proxy/photo_profil/avatar/avatar-defaut-jobpartiel.jpg" alt="Job Partiel" style="max-width: 100%; height: auto;">
          </div>

          <h2 style="color: #FF7A35; font-size: 24px; text-align: center; margin-bottom: 20px;">Réinitialisation de mot de passe</h2>
          <p style="color: #374151; font-size: 16px; line-height: 1.6; text-align: center;">
            Vous avez demandé à réinitialiser votre mot de passe. Cliquez sur le bouton ci-dessous :
          </p>

          <div style="text-align: center; margin: 25px 0;">
            <a href="${resetLink}" style="
              display: inline-block;
              background-color: #FF7A35;
              color: white;
              padding: 14px 32px;
              text-decoration: none;
              border-radius: 8px;
              font-weight: 600;
              font-size: 16px;
              transition: all 0.3s ease;
              box-shadow: 0 2px 4px rgba(255, 122, 53, 0.2);
            ">Réinitialiser mon mot de passe</a>
          </div>

          <div style="
            margin: 20px auto;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 8px;
            max-width: 90%;
          ">
            <p style="
              color: #6b7280;
              font-size: 14px;
              margin-bottom: 10px;
              text-align: center;
            ">Si le bouton ne fonctionne pas, copiez et collez ce lien dans votre navigateur :</p>
            <p style="
              background-color: white;
              padding: 12px;
              border-radius: 6px;
              border: 1px solid #e5e7eb;
              font-family: monospace;
              font-size: 13px;
              color: #374151;
              word-break: break-all;
              margin: 0;
              text-align: center;
            ">${resetLink}</p>
          </div>

          <p style="color: #374151; font-size: 14px; text-align: center; margin-top: 20px;">
            ⚠️ Ce lien expirera dans 20 minutes.
          </p>

          <p style="color: #4b5563; font-size: 14px; text-align: center; margin-top: 20px;">
            Si vous n'avez pas demandé de réinitialisation, vous pouvez ignorer cet email.
          </p>

          <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; text-align: center;">
            <small style="color: #6b7280; font-size: 12px;">
              Ceci est un email automatique, merci de ne pas y répondre.
            </small>
          </div>
        </div>
      </div>
    `
    };

    await queueEmail(mailOptions.to, mailOptions.subject, mailOptions.html);
    logSecurity.info(LogEventType.EMAIL_SENT, 'Email de réinitialisation envoyé avec succès', { to: email });
  } catch (error) {
    if (error instanceof EmailServiceError) {
      throw error;
    }

    const errorMessage = error instanceof Error ? error.message : 'Une erreur inconnue est survenue';
    throw new EmailServiceError(
      EMAIL_ERRORS.SMTP_ERROR.message(errorMessage),
      EMAIL_ERRORS.SMTP_ERROR.code,
      { originalError: errorMessage }
    );
  }
};

// Interface pour les emails de changement de statut d'abonnement
export interface SubscriptionStatusChangeEmailData {
  userName: string;
  newPlan: string;
  oldPlan?: string;
  startDate?: string;
  endDate?: string;
  changeType: 'upgrade' | 'downgrade' | 'renewal' | 'cancellation' | 'modification' | 'cancel_auto_renew' | 'reactivate_auto_renew' | 'reactivate_after_ban' | 'renewal_reminder';
  features?: Record<string, boolean>;
  html: string;
  price?: number;
  autoRenew?: boolean;
  promoCodeApplied?: string;
  discountAmount?: number;
  discountType?: 'percentage' | 'fixed';
  oldOptions?: Record<string, any>;
  newOptions?: Record<string, any>;
}

export const sendWelcomeEmail = async (email: string) => {
  try {
    // Vérification du rate limit
    checkRateLimit(email);

    const mailOptions = {
      from: `"Job Partiel" <${process.env.SMTP_FROM}>` || '<EMAIL>',
      to: email,
      subject: `${EMAIL_ICONS.welcomeEmail} ${EMAIL_SUBJECTS.welcomeEmail}`,
      html: `
      <div style="font-family: 'Inter', 'Segoe UI', Arial, sans-serif; width: 100%; max-width: 650px; margin: 0 auto; background: linear-gradient(145deg, #FFF8F3 0%, #FFE4BA 100%); padding: 40px 15px;">
        <div style="background-color: white; padding: 40px 30px; border-radius: 24px; box-shadow: 0 12px 20px rgba(255, 107, 44, 0.08);">
          <!-- En-tête avec logo et animation -->
          <div style="text-align: center; margin-bottom: 25px; position: relative;">
            <img src="${config.apiUrl}/api/storage-proxy/photo_profil/avatar/avatar-defaut-jobpartiel.jpg" alt="Job Partiel" style="max-width: 160px; height: auto; animation: fadeIn 0.8s ease-in-out;">
            <div style="position: absolute; top: -15px; right: -15px; background: #FF7A35; color: white; font-size: 14px; font-weight: bold; padding: 8px 12px; border-radius: 30px; box-shadow: 0 4px 8px rgba(255, 122, 53, 0.3); animation: bounce 2s infinite;">
              Nouveau !
            </div>
          </div>

          <!-- Titre principal avec animation -->
          <h1 style="color: #FF7A35; text-align: center; font-size: 32px; font-weight: 700; margin-bottom: 25px; position: relative; overflow: hidden;">
            <span style="display: block; animation: slideIn 0.6s ease-out;">Bienvenue sur Job Partiel !</span>
            <div style="width: 80px; height: 4px; background: linear-gradient(90deg, #FF7A35, #FFE4BA); margin: 15px auto 0; border-radius: 4px;"></div>
          </h1>

          <!-- Introduction personnalisée -->
          <p style="line-height: 1.8; color: #374151; font-size: 16px; margin-bottom: 30px; padding: 0 10px; text-align: center;">
            Félicitations ! Vous venez de rejoindre <strong>la communauté des jobbeurs</strong> qui transforment leurs compétences en opportunités
            grâce à notre plateforme innovante et notre assistant IA intelligent.
          </p>

          <!-- Bandeau d'action principal -->
          <div style="background: linear-gradient(135deg, #FF6B2C 0%, #FF965E 100%); padding: 30px; border-radius: 16px; margin: 35px 0; color: white; box-shadow: 0 8px 15px rgba(255, 107, 44, 0.2); position: relative; overflow: hidden;">
            <div style="position: absolute; top: 0; right: 0; width: 100px; height: 100px; background: rgba(255,255,255,0.1); border-radius: 0 0 0 100%"></div>

            <h2 style="margin-top: 0; margin-bottom: 20px; font-size: 24px; text-align: center; font-weight: 700;">
              🚀 Démarrez votre aventure maintenant !
            </h2>

            <ul style="padding-left: 20px; font-size: 16px; line-height: 1.8; margin-bottom: 25px;">
              <li><strong>Créez votre profil</strong> pour être visible auprès des clients potentiels</li>
              <li><strong>Définissez vos services</strong> avec l'aide de notre IA</li>
              <li><strong>Explorez les missions</strong> disponibles dans votre région</li>
              <li><strong>Échangez vos Jobis</strong> contre des services</li>
            </ul>

            <div style="text-align: center; margin-top: 15px;">
              <a href="https://jobpartiel.fr/dashboard/profil"
                 style="display: inline-block;
                        background-color: white;
                        color: #FF7A35;
                        padding: 16px 30px;
                        text-decoration: none;
                        border-radius: 12px;
                        font-weight: 700;
                        font-size: 16px;
                        transition: all 0.3s ease;
                        box-shadow: 0 4px 10px rgba(0,0,0,0.1);">
                Compléter mon profil →
              </a>
            </div>
          </div>

          <!-- Section caractéristiques principales -->
          <div style="background-color: #FFF8F3; padding: 25px; border-radius: 16px; margin: 35px 0; border: 1px solid #FFE4BA;">
            <h3 style="color: #FF7A35; margin-bottom: 20px; font-size: 22px; text-align: center; font-weight: 700;">
              <span style="display: block; margin-bottom: 15px; font-size: 30px;">✨</span>
              Notre plateforme en un coup d'œil
            </h3>

            <!-- Grille de caractéristiques -->
            <div style="display: flex; flex-wrap: wrap; gap: 15px; justify-content: center;">
              <!-- Caractéristique 1 -->
              <div style="flex: 1; min-width: 250px; background-color: white; padding: 20px; border-radius: 12px; box-shadow: 0 4px 8px rgba(0,0,0,0.05); transition: transform 0.3s ease;">
                <div style="display: flex; align-items: center; margin-bottom: 15px;">
                  <div style="background-color: #FFE4BA; width: 40px; height: 40px; border-radius: 10px; display: flex; align-items: center; justify-content: center; margin-right: 12px; flex-shrink: 0;">
                    <span style="font-size: 20px;">💼</span>
                  </div>
                  <h4 style="color: #FF7A35; margin: 0; font-size: 18px; font-weight: 700;">Services flexibles</h4>
                </div>
                <p style="color: #4B5563; font-size: 15px; line-height: 1.7; margin: 0;">
                  Proposez vos compétences selon <strong>votre emploi du temps</strong>, sans contraintes
                </p>
              </div>

              <!-- Caractéristique 2 -->
              <div style="flex: 1; min-width: 250px; background-color: white; padding: 20px; border-radius: 12px; box-shadow: 0 4px 8px rgba(0,0,0,0.05); transition: transform 0.3s ease;">
                <div style="display: flex; align-items: center; margin-bottom: 15px;">
                  <div style="background-color: #FFE4BA; width: 40px; height: 40px; border-radius: 10px; display: flex; align-items: center; justify-content: center; margin-right: 12px; flex-shrink: 0;">
                    <span style="font-size: 20px;">🔍</span>
                  </div>
                  <h4 style="color: #FF7A35; margin: 0; font-size: 18px; font-weight: 700;">Missions ciblées</h4>
                </div>
                <p style="color: #4B5563; font-size: 15px; line-height: 1.7; margin: 0;">
                  Trouvez des opportunités <strong>adaptées à vos compétences</strong> et à votre localisation
                </p>
              </div>
            </div>

            <!-- Deuxième ligne -->
            <div style="display: flex; flex-wrap: wrap; gap: 15px; justify-content: center; margin-top: 15px;">
              <!-- Caractéristique 3 -->
              <div style="flex: 1; min-width: 250px; background-color: white; padding: 20px; border-radius: 12px; box-shadow: 0 4px 8px rgba(0,0,0,0.05); transition: transform 0.3s ease;">
                <div style="display: flex; align-items: center; margin-bottom: 15px;">
                  <div style="background-color: #FFE4BA; width: 40px; height: 40px; border-radius: 10px; display: flex; align-items: center; justify-content: center; margin-right: 12px; flex-shrink: 0;">
                    <span style="font-size: 20px;">🤖</span>
                  </div>
                  <h4 style="color: #FF7A35; margin: 0; font-size: 18px; font-weight: 700;">Assistant IA</h4>
                </div>
                <p style="color: #4B5563; font-size: 15px; line-height: 1.7; margin: 0;">
                  Générez des descriptions professionnelles et <strong>optimisez votre présence</strong>
                </p>
              </div>

              <!-- Caractéristique 4 -->
              <div style="flex: 1; min-width: 250px; background-color: white; padding: 20px; border-radius: 12px; box-shadow: 0 4px 8px rgba(0,0,0,0.05); transition: transform 0.3s ease;">
                <div style="display: flex; align-items: center; margin-bottom: 15px;">
                  <div style="background-color: #FFE4BA; width: 40px; height: 40px; border-radius: 10px; display: flex; align-items: center; justify-content: center; margin-right: 12px; flex-shrink: 0;">
                    <span style="font-size: 20px;">💰</span>
                  </div>
                  <h4 style="color: #FF7A35; margin: 0; font-size: 18px; font-weight: 700;">Système Jobi</h4>
                </div>
                <p style="color: #4B5563; font-size: 15px; line-height: 1.7; margin: 0;">
                  Échangez des services contre des <strong>Jobis</strong>, notre système d'échange/troc exclusive
                </p>
              </div>
            </div>
          </div>

          <!-- Section IA spotlight -->
          <div style="margin: 35px auto; padding: 30px; background: linear-gradient(135deg, #4A6CF7 0%, #7089FA 100%); border-radius: 16px; color: white; position: relative; overflow: hidden; box-shadow: 0 10px 20px rgba(74, 108, 247, 0.15);">
            <div style="position: absolute; top: -20px; right: -20px; width: 120px; height: 120px; border-radius: 60px; background: rgba(255,255,255,0.1);"></div>
            <div style="position: absolute; bottom: -30px; left: -30px; width: 150px; height: 150px; border-radius: 75px; background: rgba(255,255,255,0.1);"></div>

            <div style="position: absolute; top: 15px; right: 15px; background-color: #FF7A35; color: white; font-size: 14px; font-weight: bold; padding: 8px 12px; border-radius: 30px; box-shadow: 0 4px 8px rgba(255, 122, 53, 0.3); animation: bounce 2s infinite;">
              10 crédits offerts !
            </div>

            <h3 style="margin-top: 0; font-size: 22px; text-align: center; font-weight: 700; margin-bottom: 20px;">
              <span style="display: block; margin-bottom: 10px; font-size: 30px;">🤖</span>
              Votre assistant IA personnel
            </h3>

            <p style="font-size: 15px; line-height: 1.7; margin-bottom: 20px; text-align: center;">
              <strong>Nous vous offrons 10 crédits IA</strong> pour créer du contenu professionnel et attirer plus de clients !
            </p>

            <div style="background: rgba(255,255,255,0.15); border-radius: 12px; padding: 15px; margin-bottom: 20px;">
              <ul style="margin: 0; padding-left: 20px; font-size: 14px; line-height: 1.7;">
                <li><strong>Générez une biographie</strong> professionnelle captivante</li>
                <li><strong>Créez des descriptions détaillées</strong> pour vos services</li>
                <li><strong>Rédigez des réponses parfaites</strong> aux avis clients</li>
                <li><strong>Optimisez vos annonces</strong> pour attirer plus de missions</li>
              </ul>
            </div>

            <div style="text-align: center;">
              <a href="https://jobpartiel.fr/dashboard/ai-credits"
                 style="display: inline-block;
                        background-color: white;
                        color: #4A6CF7;
                        padding: 14px 24px;
                        text-decoration: none;
                        border-radius: 12px;
                        font-weight: 700;
                        font-size: 15px;
                        transition: all 0.3s ease;
                        box-shadow: 0 4px 10px rgba(0,0,0,0.1);">
                Découvrir l'IA →
              </a>
            </div>
          </div>

          <!-- Témoignage client -->
          <div style="background-color: #FFF8F3; padding: 25px; border-radius: 16px; margin: 35px 0; border: 1px solid #FFE4BA; position: relative;">
            <div style="position: absolute; top: -15px; left: 30px; background: #FF7A35; color: white; font-size: 14px; font-weight: bold; padding: 5px 15px; border-radius: 20px;">
              Témoignage
            </div>

            <div style="margin-top: 15px; text-align: center;">
              <span style="color: #FF7A35; font-size: 30px;">⭐⭐⭐⭐⭐</span>
              <p style="color: #4B5563; font-style: italic; font-size: 15px; line-height: 1.7; margin: 15px 0; position: relative; padding: 0 15px;">
                "Grâce à JobPartiel, j'ai pu compléter mes revenus tout en gardant la flexibilité dont j'avais besoin pour mes études. L'assistant IA m'a aidé à créer un profil qui attire vraiment les clients!"
              </p>
              <p style="color: #FF7A35; font-weight: 700; margin: 5px 0;">Sophie M.</p>
              <p style="color: #6B7280; font-size: 14px; margin: 5px 0;">Étudiante & Jobbeur depuis 3 mois</p>
            </div>
          </div>

          <!-- Appels à l'action principaux -->
          <div style="text-align: center; margin: 35px 0; display: flex; flex-wrap: wrap; justify-content: center; gap: 15px;">
            <a href="https://jobpartiel.fr/dashboard/profil"
               style="flex: 1;
                      min-width: 200px;
                      display: inline-block;
                      background-color: #FF7A35;
                      color: white;
                      padding: 16px 20px;
                      text-decoration: none;
                      border-radius: 12px;
                      font-weight: 700;
                      font-size: 16px;
                      transition: all 0.3s ease;
                      box-shadow: 0 4px 10px rgba(255, 122, 53, 0.2);">
              <span style="display: block; margin-bottom: 5px; font-size: 20px;">👤</span>
              Mon profil
            </a>
            <a href="https://jobpartiel.fr/dashboard/missions/"
               style="flex: 1;
                      min-width: 200px;
                      display: inline-block;
                      background-color: #4A6CF7;
                      color: white;
                      padding: 16px 20px;
                      text-decoration: none;
                      border-radius: 12px;
                      font-weight: 700;
                      font-size: 16px;
                      transition: all 0.3s ease;
                      box-shadow: 0 4px 10px rgba(74, 108, 247, 0.2);">
              <span style="display: block; margin-bottom: 5px; font-size: 20px;">🔍</span>
              Explorer les missions
            </a>
          </div>

          <!-- Liens rapides -->
          <div style="display: flex; flex-wrap: wrap; gap: 10px; justify-content: center; margin: 25px 0;">
            <a href="https://jobpartiel.fr/dashboard/missions/poster-une-mission"
               style="padding: 10px 15px; background-color: #FFF8F3; color: #FF7A35; text-decoration: none; border-radius: 30px; font-size: 14px; font-weight: 500; border: 1px solid #FFE4BA;">
              🔍 Missions
            </a>
            <a href="https://jobpartiel.fr/dashboard/messages"
               style="padding: 10px 15px; background-color: #FFF8F3; color: #FF7A35; text-decoration: none; border-radius: 30px; font-size: 14px; font-weight: 500; border: 1px solid #FFE4BA;">
              💬 Messages
            </a>
            <a href="https://jobpartiel.fr/dashboard/jobi"
               style="padding: 10px 15px; background-color: #FFF8F3; color: #FF7A35; text-decoration: none; border-radius: 30px; font-size: 14px; font-weight: 500; border: 1px solid #FFE4BA;">
              💰 Mes Jobis
            </a>
            <a href="https://jobpartiel.fr/dashboard/profil/favoris"
               style="padding: 10px 15px; background-color: #FFF8F3; color: #FF7A35; text-decoration: none; border-radius: 30px; font-size: 14px; font-weight: 500; border: 1px solid #FFE4BA;">
              ❤️ Favoris
            </a>
          </div>

          <!-- Pied de page -->
          <div style="text-align: center; margin-top: 40px; padding-top: 30px; border-top: 2px solid #FFE4BA;">
            <p style="color: #6B7280; font-size: 14px; margin-bottom: 10px;">
              Besoin d'aide pour démarrer ? Notre équipe est là pour vous !
            </p>
            <a href="http://localhost:5173/dashboard/support/new"
               style="color: #FF7A35;
                      text-decoration: none;
                      font-weight: 600;
                      font-size: 14px;">
              Contacter le support
            </a>
            <div style="font-size: 12px; color: #9CA3AF; margin-top: 20px;">
              © ${new Date().getFullYear()} JobPartiel - Tous droits réservés
              <div style="margin-top: 10px;">
                <a href="https://jobpartiel.fr/conditions-generales" style="color: #9CA3AF; text-decoration: none; margin: 0 5px;">CGU</a> •
                <a href="https://jobpartiel.fr/confidentialite" style="color: #9CA3AF; text-decoration: none; margin: 0 5px;">Confidentialité</a> •
                <a href="https://jobpartiel.fr/aide" style="color: #9CA3AF; text-decoration: none; margin: 0 5px;">Aide</a>
              </div>
            </div>
          </div>
        </div>
      </div>
      <style>
        @keyframes fadeIn {
          from { opacity: 0; }
          to { opacity: 1; }
        }
        @keyframes slideIn {
          from { transform: translateY(20px); opacity: 0; }
          to { transform: translateY(0); opacity: 1; }
        }
        @keyframes bounce {
          0%, 100% { transform: translateY(0); }
          50% { transform: translateY(-5px); }
        }
        @media only screen and (max-width: 600px) {
          h1 { font-size: 26px !important; }
          h2 { font-size: 22px !important; }
          h3 { font-size: 20px !important; }
          h4 { font-size: 16px !important; }
          p, ul { font-size: 14px !important; }
          a { padding: 14px 20px !important; }
        }
      </style>
      `
    };

    await queueEmail(mailOptions.to, mailOptions.subject, mailOptions.html);
    logSecurity.info(LogEventType.EMAIL_SENT, 'Email de bienvenue envoyé avec succès', { to: email });
  } catch (error) {
    if (error instanceof EmailServiceError) {
      throw error;
    }

    const errorMessage = error instanceof Error ? error.message : 'Une erreur inconnue est survenue';
    throw new EmailServiceError(
      EMAIL_ERRORS.SMTP_ERROR.message(errorMessage),
      EMAIL_ERRORS.SMTP_ERROR.code,
      { originalError: errorMessage }
    );
  }
};

export async function sendPasswordExpirationWarningEmail(email: string, daysRemaining: number) {
  if (!isValidEmail(email)) {
    throw new EmailServiceError(
      EMAIL_ERRORS.INVALID_EMAIL.message(email),
      EMAIL_ERRORS.INVALID_EMAIL.code,
      { email }
    );
  }

  const mailOptions = {
    from: `"Job Partiel" <${process.env.SMTP_FROM}>` || '<EMAIL>',
    to: email,
    subject: `${EMAIL_ICONS.passwordExpiration} ${EMAIL_SUBJECTS.passwordExpiration}`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background-color: #f9f9f9; padding: 20px; border-radius: 8px; border: 1px solid #ddd;">
        <h2 style="color: #FF6B2C;">Votre mot de passe va bientôt expirer</h2>
        <p style="color: #555;">Votre mot de passe expirera dans ${daysRemaining} jours.</p>
        <p style="color: #555;">Pour votre sécurité, nous vous recommandons de changer votre mot de passe avant son expiration.</p>
        <p style="color: #555;">Pour changer votre mot de passe, connectez-vous à votre compte et accédez aux paramètres de votre profil.</p>
        <p style="color: #555;">Si vous ne changez pas votre mot de passe avant son expiration, vous devrez le réinitialiser pour pouvoir vous connecter.</p>
        <p style="text-align: center;">
          <a href="https://jobpartiel.fr/change-password" style="background-color: #FF7A35; color: white; padding: 14px 32px; text-decoration: none; border-radius: 8px; font-weight: 600; font-size: 16px; transition: all 0.3s ease;">Changer mon mot de passe</a>
        </p>
        <p style="color: #555;">Cordialement,<br>L'équipe Job Partiel</p>
      </div>
    `
  };

  await queueEmail(mailOptions.to, mailOptions.subject, mailOptions.html);
}

export async function sendPasswordChangedEmail(email: string) {
  if (!isValidEmail(email)) {
    throw new EmailServiceError(
      EMAIL_ERRORS.INVALID_EMAIL.message(email),
      EMAIL_ERRORS.INVALID_EMAIL.code,
      { email }
    );
  }

  const mailOptions = {
    from: `"Job Partiel" <${process.env.SMTP_FROM}>` || '<EMAIL>',
    to: email,
    subject: `${EMAIL_ICONS.passwordReset} ${EMAIL_SUBJECTS.passwordReset}`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background-color: #ffffff; padding: 30px; border: 1px solid #e0e0e0;">
        <div style="text-align: left; margin-bottom: 25px;">
          <img src="${config.apiUrl}/api/storage-proxy/photo_profil/avatar/avatar-defaut-jobpartiel.jpg" alt="Job Partiel" style="max-width: 120px; height: auto; margin-bottom: 15px;" />
          <h2 style="color: #FF6B2C; margin-top: 0; margin-bottom: 5px;">Modification de votre mot de passe</h2>
          <div style="width: 50px; height: 3px; background-color: #FF6B2C; margin-bottom: 20px;"></div>
        </div>

        <p style="color: #333; font-size: 16px; line-height: 1.5; margin-bottom: 20px;">Bonjour,</p>
        <p style="color: #333; font-size: 16px; line-height: 1.5; margin-bottom: 20px;">Votre mot de passe a été modifié avec succès.</p>

        <div style="background-color: #f8f8f8; border-left: 3px solid #FF7A35; padding: 15px; margin: 25px 0;">
          <p style="color: #333; margin: 0 0 10px 0; font-weight: 500;">Si vous n'êtes pas à l'origine de cette modification :</p>
          <ul style="color: #333; margin: 0; padding-left: 20px;">
            <li style="margin-bottom: 5px;">Réinitialisez immédiatement votre mot de passe</li>
            <li style="margin-bottom: 5px;">Contactez notre support</li>
          </ul>
        </div>

        <div style="margin-top: 30px;">
          <a href="https://jobpartiel.fr/forgot-password" style="background-color: #FF7A35; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; font-weight: 500; font-size: 14px;">Réinitialiser mon mot de passe</a>
        </div>

        <div style="margin-top: 30px; color: #555; font-size: 14px; border-top: 1px solid #eee; padding-top: 20px;">
          <p>Cordialement,<br>L'équipe Job Partiel</p>
          <p style="font-size: 12px; color: #777; margin-top: 15px;">© ${new Date().getFullYear()} Job Partiel. Tous droits réservés.</p>
        </div>
      </div>
    `
  };

  await queueEmail(mailOptions.to, mailOptions.subject, mailOptions.html);
}

export async function sendLoginAttemptsWarningEmail(email: string, attempts: number, isLocked: boolean, lockDuration?: number) {
  if (!isValidEmail(email)) {
    throw new EmailServiceError(
      EMAIL_ERRORS.INVALID_EMAIL.message(email),
      EMAIL_ERRORS.INVALID_EMAIL.code,
      { email }
    );
  }

  let statusMessage = isLocked
    ? `Votre compte a été temporairement bloqué pendant ${lockDuration} minutes pour des raisons de sécurité.`
    : `Il reste ${5 - attempts} tentatives avant que votre compte ne soit temporairement bloqué.`;

  const mailOptions = {
    from: `"Job Partiel" <${process.env.SMTP_FROM}>` || '<EMAIL>',
    to: email,
    subject: `${EMAIL_ICONS.loginAttemptWarning} ${EMAIL_SUBJECTS.loginAttemptWarning}`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background-color: #f9f9f9; padding: 20px; border-radius: 8px; border: 1px solid #ddd;">
        <h2 style="color: #e74c3c;">Alerte de sécurité</h2>
        <p style="color: #555;">Nous avons détecté plusieurs tentatives de connexion infructueuses à votre compte.</p>
        <p style="color: #555;">${statusMessage}</p>
        <p style="color: #555;">Si vous n'êtes pas à l'origine de ces tentatives, nous vous recommandons de :</p>
        <ol style="color: #555;">
          <li>Changer votre mot de passe immédiatement</li>
          <li>Activer l'authentification à deux facteurs si ce n'est pas déjà fait</li>
          <br>
          <li>Contacter notre support si vous avez des questions</li>
        </ol>
        <p style="text-align: center;">
          <a href="https://jobpartiel.fr/forgot-password" style="background-color: #FF7A35; color: white; padding: 14px 32px; text-decoration: none; border-radius: 8px; font-weight: 600; font-size: 16px; transition: all 0.3s ease;">Changer mon mot de passe</a>
        </p>
        <p style="color: #555;">Cordialement,<br>L'équipe Job Partiel</p>
      </div>
    `
  };

  await queueEmail(mailOptions.to, mailOptions.subject, mailOptions.html);
}

export const sendLoginNotificationEmail = async (email: string, city: string, ip: string, code_postal: string, country: string, region: string) => {
  console.log(`Ville transmise : ${city}`);
  const details = [];
  if (country) details.push(country);
  if (region) details.push(region);
  if (city) details.push(city);
  if (code_postal) details.push(code_postal);
  if (ip) details.push(ip);

  const message = `Un utilisateur s'est connecté avec succès à votre compte JobPartiel.fr depuis ${details.join(', ') || 'une localisation inconnue'}.`;

  const mailOptions = {
    from: `"Job Partiel" <${process.env.SMTP_FROM}>` || '<EMAIL>',
    to: email,
    subject: `${EMAIL_ICONS.newLogin} ${EMAIL_SUBJECTS.newLogin}`,
    html: `
      <div style="font-family: 'Arial', sans-serif; max-width: 600px; margin: 0 auto; background-color: #f4f4f4; padding: 20px; border-radius: 12px;">
        <div style="background-color: #ffffff; border-radius: 10px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); overflow: hidden;">
          <div style="background-color: #FF7A35; color: white; padding: 20px; text-align: center;">
            <h1 style="margin: 0; font-size: 24px;">🔔 Alerte de Connexion</h1>
          </div>
          <div style="padding: 30px; line-height: 1.6; color: #333;">
            <p style="font-size: 16px;">
              Bonjour,
            </p>
            <p style="font-size: 16px; margin-top: 15px;">
              ${message}
            </p>
            <p style="font-size: 16px;">
              ⚠️ Si vous n'êtes pas à l'origine de cette connexion, veuillez contacter notre support immédiatement.
            </p>
            <p style="font-size: 13px;">
              La localisation affichée est déterminée par votre fournisseur d'accès Internet et peut ne pas correspondre à votre position réelle.
            </p>
          </div>
        </div>
        <div style="text-align: center; margin-top: 15px; color: #666; font-size: 12px;">
          ${new Date().getFullYear()} Job Partiel. Tous droits réservés.
        </div>
      </div>
    `,
  };

  await queueEmail(mailOptions.to, mailOptions.subject, mailOptions.html);
  logSecurity.info(LogEventType.EMAIL_SENT, 'Email de notification de connexion envoyé', { to: email });
};

export const sendSuspensionEmail = async (email: string, suspensionReason: string, suspendedUntil?: string) => {
  try {
    // Vérification du rate limit
    // checkRateLimit(email);

    // Déterminer l'objet selon le message
    let subject = `${EMAIL_ICONS.suspensionEmail} ${EMAIL_SUBJECTS.suspensionEmail}`;
    if (suspensionReason && suspensionReason.toLowerCase().includes('réactivé')) {
      subject = '✅ Votre compte a été réactivé';
    }

    let html;
    if (suspensionReason && suspensionReason.toLowerCase().includes('réactivé')) {
      html = `
      <div style="font-family: 'Arial', sans-serif; max-width: 600px; margin: 0 auto; background-color: #f4f4f4; padding: 20px; border-radius: 12px;">
        <div style="background-color: #ffffff; border-radius: 10px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); overflow: hidden;">
          <div style="background-color: #4CAF50; color: white; padding: 20px; text-align: center;">
            <h1 style="margin: 0; font-size: 24px;">✅ Compte réactivé</h1>
          </div>
          <div style="padding: 30px; line-height: 1.6; color: #333;">
            <p style="font-size: 16px;">
              Bonjour,
            </p>
            <p style="font-size: 16px; margin-top: 15px;">
              Bonne nouvelle, votre compte a été <b>réactivé</b> par l'équipe de modération ! 🎉
            </p>
            <p style="color: #555; font-size: 16px; margin-top: 10px;">
              Vous pouvez à nouveau accéder à toutes les fonctionnalités de la plateforme.
            </p>
            <p style="font-size: 16px; margin-top: 10px;">
              Si vous avez des questions, n'hésitez pas à contacter notre support.
            </p>
            <p style="text-align: center;">
              <a href="http://localhost:5173/dashboard/support/new" style="background-color: #4CAF50; color: white; padding: 14px 32px; text-decoration: none; border-radius: 8px; font-weight: 600; font-size: 16px; transition: all 0.3s ease;">Contacter le support</a>
            </p>
            <p style="color: #555;">Cordialement,<br>L'équipe Job Partiel</p>
          </div>
        </div>
        <div style="text-align: center; margin-top: 15px; color: #666; font-size: 12px;">
          ${new Date().getFullYear()} Job Partiel. Tous droits réservés.
        </div>
      </div>
      `;
    } else {
      let suspensionText = '';
      if (suspendedUntil) {
        const date = new Date(suspendedUntil);
        const dateStr = date.toLocaleDateString('fr-FR');
        suspensionText = `Votre compte a été suspendu jusqu'au ${dateStr} pour la raison suivante :`;
      } else {
        suspensionText = `Votre compte a été suspendu pour la raison suivante :`;
      }
      html = `
      <div style="font-family: 'Arial', sans-serif; max-width: 600px; margin: 0 auto; background-color: #f4f4f4; padding: 20px; border-radius: 12px;">
        <div style="background-color: #ffffff; border-radius: 10px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); overflow: hidden;">
          <div style="background-color: #FF7A35; color: white; padding: 20px; text-align: center;">
            <h1 style="margin: 0; font-size: 24px;">🚫 Suspension de Compte</h1>
          </div>
          <div style="padding: 30px; line-height: 1.6; color: #333;">
            <p style="font-size: 16px;">
              Bonjour,
            </p>
            <p style="font-size: 16px; margin-top: 15px;">
              ${suspensionText}
            </p>
            <p style="color: #555; font-weight: bold; font-size: 16px;">${suspensionReason}</p>
            <p style="font-size: 16px;">
              Si vous pensez que cela est une erreur, veuillez contacter notre support.
            </p>
            <p style="text-align: center;">
              <a href="http://localhost:5173/dashboard/support/new" style="background-color: #FF7A35; color: white; padding: 14px 32px; text-decoration: none; border-radius: 8px; font-weight: 600; font-size: 16px; transition: all 0.3s ease;">Contacter le support</a>
            </p>
            <p style="color: #555;">Cordialement,<br>L'équipe Job Partiel</p>
          </div>
        </div>
        <div style="text-align: center; margin-top: 15px; color: #666; font-size: 12px;">
          ${new Date().getFullYear()} Job Partiel. Tous droits réservés.
        </div>
      </div>
      `;
    }
    const mailOptions = {
      from: `"Job Partiel" <${process.env.SMTP_FROM}>` || '<EMAIL>',
      to: email,
      subject,
      html
    };

    await queueEmail(mailOptions.to, mailOptions.subject, mailOptions.html);
    logSecurity.info(LogEventType.EMAIL_SENT, 'Email de suspension envoyé avec succès', { to: email });
  } catch (error) {
    if (error instanceof EmailServiceError) {
      throw error;
    }

    const errorMessage = error instanceof Error ? error.message : 'Une erreur inconnue est survenue';
    throw new EmailServiceError(
      EMAIL_ERRORS.SMTP_ERROR.message(errorMessage),
      EMAIL_ERRORS.SMTP_ERROR.code,
      { originalError: errorMessage }
    );
  }
};

interface MissionPhotoData {
  photoCount: number;
  missions: Array<{
    title: string;
    description: string;
    budget: number;
    date: string | null;
    location: string;
    photoCount: number;
  }>;
}

export const sendPhotosDeletionEmail = async (email: string, data: MissionPhotoData) => {
  try {
    // Vérification du rate limit, aucun pour cette fonction car géré par le backend
    // checkRateLimit(email);

    const formatDate = (date: string | null) => {
      if (!date) return 'Non spécifiée';
      return new Date(date).toLocaleDateString('fr-FR', {
        day: 'numeric',
        month: 'long',
        year: 'numeric'
      });
    };

    const formatBudget = (budget: number) => {
      return new Intl.NumberFormat('fr-FR', {
        style: 'currency',
        currency: 'EUR'
      }).format(budget);
    };

    const mailOptions = {
      from: `"Job Partiel" <${process.env.SMTP_FROM}>` || '<EMAIL>',
      to: email,
      subject: `${EMAIL_ICONS.photosDeletion} ${EMAIL_SUBJECTS.photosDeletion}`,
      html: `
      <div style="font-family: 'Arial', sans-serif; width: 100%; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #FFF8F3 0%, #FFE4BA 100%); padding: 40px 0;">
        <div style="background-color: white; padding: 40px 20px; border-radius: 16px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
          <div style="text-align: center; margin-bottom: 30px;">
            <img src="${config.apiUrl}/api/storage-proxy/photo_profil/avatar/avatar-defaut-jobpartiel.jpg" alt="Job Partiel" style="max-width: 100%; height: auto;">
          </div>

          <h2 style="color: #FF7A35; text-align: center; font-size: 28px; margin-bottom: 25px;">Suppression des photos de vos missions</h2>

          <p style="line-height: 1.8; color: #374151; font-size: 16px;">
            Conformément à notre politique de conservation des données et dans un souci écologique, nous vous informons que ${data.photoCount} photo${data.photoCount > 1 ? 's' : ''} de vos missions ${data.photoCount > 1 ? 'ont été supprimées' : 'a été supprimée'} après 90 jours de conservation.
          </p>

          <div style="background-color: #FFF8F3; padding: 20px; border-radius: 12px; margin: 20px 0;">
            <h3 style="color: #FF7A35; font-size: 20px; margin: 0 0 15px 0;">
              Détails des missions concernées :
            </h3>
            ${data.missions.map((mission, index) => `
              <div style="background-color: white; padding: 20px; border-radius: 8px; margin-bottom: ${index < data.missions.length - 1 ? '15px' : '0'};">
                <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 10px;">
                  <h4 style="color: #FF7A35; margin: 0; font-size: 18px;">
                    ${mission.title}
                  </h4>
                  <span style="background-color: #FFF8F3; color: #FF7A35; padding: 4px 8px; border-radius: 4px; font-size: 14px; font-weight: 600;">
                    ${mission.photoCount} photo${mission.photoCount > 1 ? 's' : ''}
                  </span>
                </div>
                <div style="color: #374151; font-size: 14px; line-height: 1.6;">
                  <p style="margin: 0 0 8px 0;">
                    <strong>Description :</strong><br>
                    ${mission.description}
                  </p>
                  <p style="margin: 0 0 8px 0;">
                    <strong>Budget :</strong> ${formatBudget(mission.budget)}
                  </p>
                  <p style="margin: 0 0 8px 0;">
                    <strong>Date :</strong> ${formatDate(mission.date)}
                  </p>
                  <p style="margin: 0;">
                    <strong>Lieu :</strong> ${mission.location}
                  </p>
                </div>
              </div>
            `).join('')}
          </div>

          <p style="line-height: 1.8; color: #374151; font-size: 16px;">
            Cette action s'inscrit dans notre démarche de réduction de l'empreinte carbone numérique. Les photos ont été remplacées par une image par défaut pour maintenir l'intégrité de vos missions.
          </p>

          <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #E5E7EB; text-align: center;">
            <p style="color: #6B7280; font-size: 14px;">
              Pour toute question, n'hésitez pas à contacter notre support à <a href="http://localhost:5173/dashboard/support/new" style="color: #FF7A35; text-decoration: none;">Contacter le support</a>
            </p>
          </div>
        </div>
      </div>
      <style>
        @media only screen and (max-width: 600px) {
          h2 { font-size: 24px; }
          h3 { font-size: 18px; }
          h4 { font-size: 16px; }
          p { font-size: 14px; }
        }
      </style>
    `
    };

    await queueEmail(mailOptions.to, mailOptions.subject, mailOptions.html);

    logSecurity.info(LogEventType.EMAIL_SENT, 'Email de notification de suppression des photos envoyé', {
      to: email,
      photoCount: data.photoCount,
      missionCount: data.missions.length
    });
  } catch (error) {
    logSecurity.error(LogEventType.EMAIL_ERROR, 'Erreur lors de l\'envoi de l\'email de notification de suppression des photos', {
      error,
      email
    });
    throw error;
  }
};

export const sendNewMissionEmail = async (email: string, missionData: {
  missionTitle: string;
  missionDescription: string;
  missionBudget: number;
  missionLocation: string;
  missionDate: string | null;
  timeSlots: Array<{
    date: string;
    start: string;
    end: string;
  }>;
  missionUrl: string;
  budget_defini: boolean;
  payment_method: 'jobi_only' | 'both' | 'direct_only';
  category_id: string;
}) => {
  try {
    // Vérification du rate limit
    // checkRateLimit(email); // Pas de rate limit pour cette fonction car géré par le backend

    // Mapping des catégories
    const categories: { [key: string]: string } = {
      '1': 'Jardinage',
      '2': 'Bricolage',
      '3': 'Garde d\'animaux',
      '4': 'Services à la personne',
      '5': 'Événementiel & Restauration',
      '6': 'Services administratifs',
      '7': 'Transport & Logistique',
      '8': 'Communication & Marketing',
      '9': 'Éducation & Formation',
      '10': 'Informatique',
      '11': 'Arts & Divertissement',
      '12': 'Bien-être & Santé',
      '13': 'Services aux entreprises',
      '14': 'Artisanat & Création',
      '15': 'Sport & Loisirs',
      '16': 'Immobilier & Habitat',
      '17': 'Automobile & Transport',
      '18': 'Décoration & Design',
      '19': 'Services financiers',
      '20': 'Tourisme & Voyages',
      '21': 'Rénovation & Travaux',
      '22': 'Piscine & Spa',
      '23': 'Mode & Beauté',
      '24': 'Sécurité & Protection',
      '25': 'Environnement & Écologie'
    };

    // Debug
    console.log('Info du MissionData', missionData);

    // Extraire la ville de missionLocation (prendre le premier mot après la virgule s'il y en a une)
    const city = missionData.missionLocation.split(',')[0].trim();

    // Obtenir la catégorie de manière sécurisée
    let category = 'Service';
    if (missionData.category_id) {
      category = categories[missionData.category_id] || 'Service';
    }

    // Tronquer le titre à 20 caractères
    const truncatedTitle = missionData.missionTitle.length > 20
      ? missionData.missionTitle.substring(0, 20) + '...'
      : missionData.missionTitle;

    const formatTimeSlot = (slot: { date: string; start: string; end: string }) => {
      const date = new Date(slot.date);
      const formattedDate = date.toLocaleDateString('fr-FR', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
      return `${formattedDate} de ${slot.start} à ${slot.end}`;
    };

    const timeSlotsHtml = missionData.timeSlots && missionData.timeSlots.length > 0
      ? `
        <div style="margin-top: 15px;">
          <h4 style="color: #FF7A35; margin-bottom: 10px;">Créneaux horaires proposés :</h4>
          <ul style="list-style-type: none; padding: 0;">
            ${missionData.timeSlots.map(slot => `
              <li style="background-color: #FFF8F3; padding: 10px; margin-bottom: 8px; border-radius: 8px;">
                📅 ${formatTimeSlot(slot)}
              </li>
            `).join('')}
          </ul>
        </div>
      `
      : `
        <div style="margin-top: 15px;">
          <h4 style="color: #FF7A35; margin-bottom: 10px;">Disponibilité :</h4>
          <p style="background-color: #FFF8F3; padding: 10px; margin-bottom: 8px; border-radius: 8px;">
            📅 Pas de préférence horaire - Flexible selon vos disponibilités
          </p>
        </div>
      `;

    const mailOptions = {
      from: `"Job Partiel" <${process.env.SMTP_FROM}>` || '<EMAIL>',
      to: email,
      subject: `${EMAIL_ICONS.newMission} ${EMAIL_SUBJECTS.newMission}: ${category} à ${city} : ${truncatedTitle}`,
      html: `
      <div style="font-family: 'Arial', sans-serif; width: 100%; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #FFF8F3 0%, #FFE4BA 100%); padding: 40px 0;">
        <div style="background-color: white; padding: 40px 20px; border-radius: 16px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
          <div style="text-align: center; margin-bottom: 30px;">
            <img src="${config.apiUrl}/api/storage-proxy/photo_profil/avatar/avatar-defaut-jobpartiel.jpg" alt="Job Partiel" style="max-width: 100%; height: auto;">
          </div>

          <div style="text-align: center; background-color: #FFF8F3; padding: 20px; border-radius: 12px; margin-bottom: 30px;">
            <div style="background-color: #FF7A35; display: inline-block; border-radius: 50%; width: 80px; height: 80px; line-height: 80px; text-align: center; margin-bottom: 15px;">
              <span style="font-size: 40px; color: white;">🔔</span>
            </div>
            <h2 style="color: #FF7A35; font-size: 28px; margin: 0 0 10px 0;">
              Nouvelle Mission Disponible !
            </h2>
            <p style="color: #374151; font-size: 16px; margin: 0;">
              Une opportunité dans votre zone d'intervention
            </p>
          </div>

          <p style="line-height: 1.8; color: #374151; font-size: 16px;">
            Bonjour,
          </p>

          <p style="line-height: 1.8; color: #374151; font-size: 16px;">
            Une nouvelle mission correspondant à vos compétences et à votre zone d'intervention vient d'être publiée. Voici les détails :
          </p>

          <div style="background-color: #FFF8F3; padding: 25px; border-radius: 12px; margin: 25px 0;">
            <div style="display: flex; align-items: center; margin-bottom: 20px;">
              <div style="background-color: white; border-radius: 50%; width: 50px; height: 50px; display: flex; align-items: center; justify-content: center; margin-right: 15px; flex-shrink: 0;">
                <span style="font-size: 24px;">${category === 'Jardinage' ? '🌿' : category === 'Bricolage' ? '🔧' : category === 'Garde d\'animaux' ? '🐾' : '🛠️'}</span>
              </div>
              <h3 style="color: #FF7A35; margin: 0; font-size: 22px;">
                ${missionData.missionTitle}
              </h3>
            </div>

            <div style="background-color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
              <p style="margin: 0 0 15px 0; color: #374151; font-weight: bold;">Description :</p>
              <p style="margin: 0; color: #4B5563; line-height: 1.6;">
                ${missionData.missionDescription}
              </p>
            </div>

            <div style="display: flex; flex-wrap: wrap; gap: 15px; margin-bottom: 20px;">
              <div style="flex: 1; min-width: 200px; background-color: white; padding: 15px; border-radius: 8px; border-left: 4px solid #FF7A35;">
                <div style="display: flex; align-items: center;">
                  <div style="margin-right: 10px; background-color: #FFF8F3; border-radius: 50%; width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;">
                    <span style="font-size: 20px;">💰</span>
                  </div>
                  <div>
                    <p style="margin: 0; color: #6B7280; font-size: 14px;">Budget</p>
                    <p style="margin: 5px 0 0 0; color: #374151; font-weight: bold;">
                      ${
                        !missionData.budget_defini ? 'À définir' :
                        missionData.payment_method === 'jobi_only' ? `${missionData.missionBudget} Jobi maximum` :
                        missionData.payment_method === 'direct_only' ? `${new Intl.NumberFormat('fr-FR', {
                          style: 'currency',
                          currency: 'EUR'
                        }).format(missionData.missionBudget)} maximum` :
                        `${missionData.missionBudget} Jobi ou ${new Intl.NumberFormat('fr-FR', {
                          style: 'currency',
                          currency: 'EUR'
                        }).format(missionData.missionBudget)} maximum`
                      }
                    </p>
                  </div>
                </div>
              </div>

              <div style="flex: 1; min-width: 200px; background-color: white; padding: 15px; border-radius: 8px; border-left: 4px solid #FF7A35;">
                <div style="display: flex; align-items: center;">
                  <div style="margin-right: 10px; background-color: #FFF8F3; border-radius: 50%; width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;">
                    <span style="font-size: 20px;">📍</span>
                  </div>
                  <div>
                    <p style="margin: 0; color: #6B7280; font-size: 14px;">Lieu</p>
                    <p style="margin: 5px 0 0 0; color: #374151; font-weight: bold;">
                      ${missionData.missionLocation}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div style="background-color: white; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
              <div style="display: flex; align-items: center; margin-bottom: 10px;">
                <div style="margin-right: 10px; background-color: #FFF8F3; border-radius: 50%; width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;">
                  <span style="font-size: 20px;">💳</span>
                </div>
                <div>
                  <p style="margin: 0; color: #6B7280; font-size: 14px;">Mode de paiement</p>
                  <p style="margin: 5px 0 0 0; color: #374151; font-weight: bold;">
                    ${
                      missionData.payment_method === 'jobi_only' ?
                      'Troc/échange en Jobi uniquement' :
                      missionData.payment_method === 'direct_only' ?
                      'Paiement direct uniquement' :
                      'Troc/échange en Jobi ou paiement direct'
                    }
                  </p>
                </div>
              </div>
              <p style="margin: 0; color: #6B7280; font-size: 14px; font-style: italic;">
                ${
                  missionData.payment_method === 'jobi_only' ?
                  '✅ Transaction sécurisée via la plateforme avec protection maximale' :
                  missionData.payment_method === 'direct_only' ?
                  '⚠️ Arrangement direct avec le client (sans protection plateforme)' :
                  '🔄 Le client accepte les deux modes de paiement, à vous de choisir'
                }
              </p>
            </div>

            <div style="background-color: white; padding: 15px; border-radius: 8px;">
              <div style="display: flex; align-items: flex-start;">
                <div style="margin-right: 10px; background-color: #FFF8F3; border-radius: 50%; width: 40px; height: 40px; display: flex; align-items: center; justify-content: center; margin-top: 5px;">
                  <span style="font-size: 20px;">📅</span>
                </div>
                <div style="flex: 1;">
                  <p style="margin: 0 0 10px 0; color: #6B7280; font-size: 14px;">Disponibilité</p>
                  ${missionData.timeSlots && missionData.timeSlots.length > 0 ? `
                    <ul style="list-style-type: none; padding: 0; margin: 0;">
                      ${missionData.timeSlots.map(slot => `
                        <li style="background-color: #FFF8F3; padding: 10px; margin-bottom: 8px; border-radius: 8px; color: #374151; font-weight: 500;">
                          ${formatTimeSlot(slot)}
                        </li>
                      `).join('')}
                    </ul>
                  ` : `
                    <p style="background-color: #FFF8F3; padding: 10px; margin: 0; border-radius: 8px; color: #374151; font-weight: 500;">
                      Flexible selon vos disponibilités
                    </p>
                  `}
                </div>
              </div>
            </div>
          </div>

          <div style="background-color: #FFF8F3; padding: 25px; border-radius: 12px; margin: 30px 0;">
            <h3 style="color: #FF7A35; margin: 0 0 15px 0; font-size: 20px;">
              Pourquoi postuler à cette mission ?
            </h3>
            <ul style="color: #374151; font-size: 16px; line-height: 1.8; margin: 0; padding-left: 20px;">
              <li><strong>Correspond à vos compétences</strong> - Cette mission est dans votre domaine d'expertise</li>
              <li><strong>Proche de chez vous</strong> - Située dans votre zone d'intervention</li>
              <li><strong>Opportunité de revenus</strong> - Complétez vos revenus avec cette mission</li>
              <li><strong>Développez votre réseau</strong> - Un client satisfait peut devenir un client régulier</li>
            </ul>
          </div>

          <div style="text-align: center; margin-top: 35px 0;">
            <a href="${missionData.missionUrl}"
               style="display: inline-block;
                      background-color: #FF7A35;
                      color: white;
                      padding: 16px 40px;
                      text-decoration: none;
                      border-radius: 12px;
                      font-weight: 600;
                      font-size: 18px;
                      transition: all 0.3s ease;
                      box-shadow: 0 4px 6px rgba(255, 122, 53, 0.2);">
              Voir cette mission
            </a>
          </div>

          <div style="
            margin: 30px auto;
            padding: 25px;
            background: linear-gradient(135deg, #FFF8F3 0%, #FFE4BA 100%);
            border-radius: 12px;
            max-width: 90%;
          ">
            <p style="
              color: #374151;
              font-size: 14px;
              margin-bottom: 15px;
              text-align: center;
            ">Si le bouton ne fonctionne pas, copiez et collez ce lien dans votre navigateur :</p>
            <p style="
              background-color: white;
              padding: 15px;
              border-radius: 8px;
              border: 2px solid #FFE4BA;
              font-family: monospace;
              font-size: 14px;
              color: #374151;
              word-break: break-all;
              margin: 0;
              text-align: center;
            ">${missionData.missionUrl}</p>
          </div>

          <div style="text-align: center; margin-top: 30px; padding-top: 30px; border-top: 2px solid #FFE4BA;">
            <p style="color: #4B5563; font-size: 14px; margin-bottom: 10px;">
              Vous recevez cet email car cette mission correspond à vos services et votre zone d'intervention.
            </p>
            <a href="https://jobpartiel.fr/parametres/notifications"
               style="color: #FF7A35;
                      text-decoration: none;
                      font-weight: 600;
                      font-size: 14px;">
              Gérer mes notifications
            </a>
          </div>
        </div>
        <div style="text-align: center; margin-top: 15px; color: #666; font-size: 12px;">
          © ${new Date().getFullYear()} Job Partiel. Tous droits réservés.
        </div>
      </div>
      <style>
        @media only screen and (max-width: 600px) {
          h2 { font-size: 24px; }
          h3 { font-size: 18px; }
          p { font-size: 14px; }
          div[style*="display: flex"] { flex-direction: column; }
          div[style*="flex: 1"] { width: 100%; margin: 0 0 15px 0 !important; }
          div[style*="margin-right: 15px"] { margin: 0 0 10px 0 !important; }
        }
      </style>
    `
    };

    await queueEmail(mailOptions.to, mailOptions.subject, mailOptions.html);
    logSecurity.info(LogEventType.EMAIL_SENT, 'Email de notification de nouvelle mission envoyé', { to: email });
  } catch (error) {
    if (error instanceof EmailServiceError) {
      throw error;
    }

    const errorMessage = error instanceof Error ? error.message : 'Une erreur inconnue est survenue';
    throw new EmailServiceError(
      EMAIL_ERRORS.SMTP_ERROR.message(errorMessage),
      EMAIL_ERRORS.SMTP_ERROR.code,
      { originalError: errorMessage }
    );
  }
};

export const sendMissionAutoClosureEmail = async (email: string, missionData: {
  titre: string;
  description: string;
  budget: number | null;
  budget_defini: boolean;
  adresse: string;
  ville: string;
  code_postal: string;
  date_mission: string | null;
  category_id: string;
  payment_method: 'jobi_only' | 'both' | 'direct_only';
  jobi_deduction?: number;
}) => {
  try {
    // Mapping des catégories
    const categories: { [key: string]: string } = {
      '1': 'Jardinage',
      '2': 'Bricolage',
      '3': 'Garde d\'animaux',
      '4': 'Services à la personne',
      '5': 'Événementiel & Restauration',
      '6': 'Services administratifs',
      '7': 'Transport & Logistique',
      '8': 'Communication & Marketing',
      '9': 'Éducation & Formation',
      '10': 'Informatique',
      '11': 'Arts & Divertissement',
      '12': 'Bien-être & Santé',
      '13': 'Services aux entreprises',
      '14': 'Artisanat & Création',
      '15': 'Sport & Loisirs',
      '16': 'Immobilier & Habitat',
      '17': 'Automobile & Transport',
      '18': 'Décoration & Design',
      '19': 'Services financiers',
      '20': 'Tourisme & Voyages',
      '21': 'Rénovation & Travaux',
      '22': 'Piscine & Spa',
      '23': 'Mode & Beauté',
      '24': 'Sécurité & Protection',
      '25': 'Environnement & Écologie'
    };

    const mailOptions = {
      from: `"Job Partiel" <${process.env.SMTP_FROM}>` || '<EMAIL>',
      to: email,
      subject: `${EMAIL_ICONS.missionAutoClosure} ${EMAIL_SUBJECTS.missionAutoClosure}: "${missionData.titre}"`,
      html: `
      <div style="font-family: 'Arial', sans-serif; width: 100%; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #FFF8F3 0%, #FFE4BA 100%); padding: 40px 0;">
        <div style="background-color: white; padding: 40px; border-radius: 16px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
          <div style="text-align: center; margin-bottom: 30px;">
            <img src="${config.apiUrl}/api/storage-proxy/photo_profil/avatar/avatar-defaut-jobpartiel.jpg" alt="Job Partiel" style="max-width: 100%; height: auto;">
          </div>

          <h2 style="color: #FF7A35; text-align: center; font-size: 28px; margin-bottom: 25px;">
            Fermeture automatique de votre mission
          </h2>

          <div style="background-color: #FFF8F3; padding: 25px; border-radius: 12px; margin: 30px 0;">
            <h3 style="color: #FF7A35; margin-bottom: 15px; font-size: 20px;">
              Détails de la mission :
            </h3>

            <div style="color: #374151; font-size: 16px; line-height: 1.8;">
              <p style="margin-bottom: 15px;">
                <strong>Titre :</strong> ${missionData.titre}
              </p>

              <p style="margin-bottom: 15px;">
                <strong>Description :</strong><br>
                ${missionData.description}
              </p>

              <p style="margin-bottom: 15px;">
                <strong>Catégorie :</strong> ${categories[missionData.category_id] || 'Non spécifiée'}
              </p>

              <p style="margin-bottom: 15px;">
                <strong>Budget :</strong> ${
                  !missionData.budget_defini ? 'À définir' :
                  missionData.payment_method === 'jobi_only' ? `${missionData.budget} Jobi maximum` :
                  missionData.payment_method === 'direct_only' ? `${new Intl.NumberFormat('fr-FR', {
                    style: 'currency',
                    currency: 'EUR'
                  }).format(missionData.budget || 0)} maximum` :
                  `${missionData.budget} Jobi ou ${new Intl.NumberFormat('fr-FR', {
                    style: 'currency',
                    currency: 'EUR'
                  }).format(missionData.budget || 0)} maximum`
                }
              </p>

              <p style="margin-bottom: 15px;">
                <strong>Lieu :</strong> ${missionData.adresse}, ${missionData.code_postal} ${missionData.ville}
              </p>

              ${missionData.date_mission ? `
                <p style="margin-bottom: 15px;">
                  <strong>Date prévue :</strong> ${new Date(missionData.date_mission).toLocaleDateString('fr-FR', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </p>
              ` : ''}
            </div>
          </div>

          <div style="background-color: #FFF8F3; padding: 20px; border-radius: 12px; margin: 20px 0;">
            <h4 style="color: #FF7A35; margin-bottom: 15px; font-size: 18px;">
              Pourquoi cette fermeture automatique ?
            </h4>
            <p style="color: #374151; font-size: 16px; line-height: 1.6;">
              Votre mission a été automatiquement fermée après 60 jours d'inactivité. Cette mesure s'inscrit dans notre démarche d'écologie numérique et vise à :
            </p>
            <ul style="color: #374151; font-size: 16px; line-height: 1.6; margin: 15px 0;">
              <li>Réduire notre empreinte carbone en optimisant nos requêtes en base de données</li>
              <li>Maintenir une plateforme dynamique avec des missions actives et pertinentes</li>
              <li>Offrir aux jobbeurs une meilleure visibilité sur les missions réellement disponibles</li>
            </ul>
          </div>

          ${missionData.jobi_deduction ? `
          <div style="background-color: #FFF8F3; padding: 20px; border-radius: 12px; margin: 20px 0; border: 1px solid #FF7A35;">
            <h4 style="color: #FF7A35; margin-bottom: 15px; font-size: 18px;">
              Information importante concernant vos Jobis
            </h4>
            <p style="color: #374151; font-size: 16px; line-height: 1.6;">
              Conformément à nos conditions d'utilisation, <strong>${missionData.jobi_deduction} Jobi</strong> ${missionData.jobi_deduction === 1 ? 'a été déduit' : 'ont été déduits'} de votre solde suite à la fermeture automatique de cette mission.
            </p>
            <p style="color: #374151; font-size: 16px; line-height: 1.6; margin-top: 10px;">
              Pour rappel, lors de la création d'une mission, vous recevez 1 Jobi en récompense. Cette récompense est annulée lorsqu'une mission est fermée automatiquement sans avoir été complétée.
            </p>
          </div>
          ` : ''}

          <div style="text-align: center; margin: 35px 0;">
            <a href="https://jobpartiel.fr/dashboard/missions/poster-une-mission"
               style="display: inline-block;
                      background-color: #FF7A35;
                      color: white;
                      padding: 16px 40px;
                      text-decoration: none;
                      border-radius: 12px;
                      font-weight: 600;
                      font-size: 18px;
                      transition: all 0.3s ease;
                      box-shadow: 0 4px 6px rgba(255, 122, 53, 0.2);">
              Poster une nouvelle mission
            </a>
          </div>
        </div>
      </div>
      <style>
        @media only screen and (max-width: 600px) {
          h2 { font-size: 24px; }
          h3 { font-size: 18px; }
          h4 { font-size: 16px; }
          p, ul { font-size: 14px; }
        }
      </style>
    `
    };

    await queueEmail(mailOptions.to, mailOptions.subject, mailOptions.html);
    logSecurity.info(LogEventType.EMAIL_SENT, 'Email de fermeture automatique de mission envoyé', { to: email });
  } catch (error) {
    if (error instanceof EmailServiceError) {
      throw error;
    }

    const errorMessage = error instanceof Error ? error.message : 'Une erreur inconnue est survenue';
    throw new EmailServiceError(
      EMAIL_ERRORS.SMTP_ERROR.message(errorMessage),
      EMAIL_ERRORS.SMTP_ERROR.code,
      { originalError: errorMessage }
    );
  }
};

export const sendNewProposalEmail = async (email: string, data: {
  missionTitle: string;
  jobbeurName: string;
  amount: number;
  message: string;
  missionId: string;
}) => {
  try {
    const mailOptions = {
      from: `"Job Partiel" <${process.env.SMTP_FROM}>` || '<EMAIL>',
      to: email,
      subject: `${EMAIL_ICONS.newProposal} ${EMAIL_SUBJECTS.newProposal}: "${data.missionTitle}"`,
      html: `
        <div style="font-family: 'Arial', sans-serif; width: 100%; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #FFF8F3 0%, #FFE4BA 100%); padding: 40px 0;">
          <div style="background-color: white; padding: 40px 20px; border-radius: 16px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
            <div style="text-align: center; margin-bottom: 30px;">
              <img src="${config.apiUrl}/api/storage-proxy/photo_profil/avatar/avatar-defaut-jobpartiel.jpg" alt="Job Partiel" style="max-width: 100%; height: auto;">
            </div>

            <div style="text-align: center; background-color: #FFF8F3; padding: 20px; border-radius: 12px; margin-bottom: 30px;">
              <div style="background-color: #FF7A35; display: inline-block; border-radius: 50%; width: 80px; height: 80px; line-height: 80px; text-align: center; margin-bottom: 15px;">
                <span style="font-size: 40px; color: white;">💼</span>
              </div>
              <h2 style="color: #FF7A35; font-size: 28px; margin: 0 0 10px 0;">
                Nouvelle Proposition Reçue !
              </h2>
              <p style="color: #374151; font-size: 16px; margin: 0;">
                Pour votre mission "${data.missionTitle}"
              </p>
            </div>

            <p style="line-height: 1.8; color: #374151; font-size: 16px;">
              Bonjour,
            </p>

            <p style="line-height: 1.8; color: #374151; font-size: 16px;">
              <strong>${data.jobbeurName}</strong> vient de vous faire une proposition pour votre mission. Voici les détails :
            </p>

            <div style="background-color: #FFF8F3; padding: 25px; border-radius: 12px; margin: 30px 0; border: 2px solid #FF7A35;">
              <h3 style="color: #FF7A35; margin: 0 0 10px 0; font-size: 18px; display: flex; align-items: center;">
                <span style="font-size: 22px; margin-right: 8px;">⏰</span>Répondez sous 24h
              </h3>
              <p style="color: #374151; font-size: 16px; line-height: 1.8; margin: 0;">
                <strong>Vous disposez de 24h pour répondre à cette proposition.</strong><br>
                Passé ce délai, elle sera automatiquement refusée.
              </p>
            </div>

            <div style="background-color: #FFF8F3; padding: 25px; border-radius: 12px; margin: 25px 0;">
              <div style="display: flex; align-items: center; margin-bottom: 20px;">
                <div style="background-color: white; border-radius: 50%; width: 50px; height: 50px; display: flex; align-items: center; justify-content: center; margin-right: 15px; flex-shrink: 0;">
                  <span style="font-size: 24px;">💰</span>
                </div>
                <div>
                  <h3 style="color: #FF7A35; margin: 0 0 5px 0; font-size: 20px;">
                    Montant proposé
                  </h3>
                  <p style="color: #374151; font-size: 24px; font-weight: bold; margin: 0;">
                    ${data.amount}€
                  </p>
                </div>
              </div>

              <div style="background-color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                <p style="margin: 0 0 15px 0; color: #374151; font-weight: bold;">Message du jobbeur :</p>
                <p style="margin: 0; color: #4B5563; line-height: 1.6; font-style: italic;">
                  "${data.message}"
                </p>
              </div>
            </div>

            <div style="background-color: #FFF8F3; padding: 25px; border-radius: 12px; margin: 30px 0;">
              <h3 style="color: #FF7A35; margin: 0 0 15px 0; font-size: 20px;">
                <span style="margin-right: 10px;">💡</span>Conseils pour évaluer cette proposition
              </h3>
              <ul style="color: #374151; font-size: 16px; line-height: 1.8; margin: 0; padding-left: 20px;">
                <li><strong>Vérifiez le profil du jobbeur</strong> - Consultez ses évaluations et son expérience</li>
                <li><strong>Comparez avec d'autres offres</strong> - Attendez plusieurs propositions avant de décider</li>
                <li><strong>Négociez si nécessaire</strong> - N'hésitez pas à faire une contre-offre</li>
                <li><strong>Clarifiez les détails</strong> - Assurez-vous que le jobbeur a bien compris vos attentes</li>
              </ul>
            </div>

            <div style="background-color: #FFF8F3; padding: 25px; border-radius: 12px; margin: 30px 0;">
              <h3 style="color: #FF7A35; margin: 0 0 15px 0; font-size: 20px;">
                <span style="margin-right: 10px;">⚠️</span>Points importants à retenir
              </h3>
              <ul style="color: #374151; font-size: 16px; line-height: 1.8; margin: 0; padding-left: 20px;">
                <li><strong>Ne communiquez jamais vos coordonnées bancaires</strong> - Utilisez uniquement les moyens de paiement sécurisés de la plateforme</li>
                <li><strong>Privilégiez les paiements en Jobi</strong> - Ils offrent une protection supplémentaire</li>
                <li><strong>Définissez clairement les attentes</strong> - Précisez les délais et les résultats attendus</li>
              </ul>
            </div>

            <div style="text-align: center; margin: 35px 0;">
              <a href="${process.env.FRONTEND_URL}/dashboard/missions/offres?tab=1&mission=${data.missionId}"
                 style="display: inline-block;
                        background-color: #FF7A35;
                        color: white;
                        padding: 16px 40px;
                        text-decoration: none;
                        border-radius: 12px;
                        font-weight: 600;
                        font-size: 18px;
                        transition: all 0.3s ease;
                        box-shadow: 0 4px 6px rgba(255, 122, 53, 0.2);">
                Voir la proposition
              </a>
            </div>

            <div style="
              margin: 30px auto;
              padding: 25px;
              background: linear-gradient(135deg, #FFF8F3 0%, #FFE4BA 100%);
              border-radius: 12px;
              max-width: 90%;
            ">
              <p style="
                color: #374151;
                font-size: 14px;
                margin-bottom: 15px;
                text-align: center;
              ">Si le bouton ne fonctionne pas, copiez et collez ce lien dans votre navigateur :</p>
              <p style="
                background-color: white;
                padding: 15px;
                border-radius: 8px;
                border: 2px solid #FFE4BA;
                font-family: monospace;
                font-size: 14px;
                color: #374151;
                word-break: break-all;
                margin: 0;
                text-align: center;
              ">${process.env.FRONTEND_URL}/dashboard/missions/offres?tab=1&mission=${data.missionId}</p>
            </div>

            <div style="text-align: center; margin-top: 30px; padding-top: 30px; border-top: 2px solid #FFE4BA;">
              <p style="color: #4B5563; font-size: 14px; margin-bottom: 10px;">
                Besoin d'aide pour évaluer cette proposition ? Notre équipe est là pour vous guider.
              </p>
              <a href="http://localhost:5173/dashboard/support/new"
                 style="color: #FF7A35;
                        text-decoration: none;
                        font-weight: 600;
                        font-size: 14px;">
                Contacter le support
              </a>
            </div>
          </div>
          <div style="text-align: center; margin-top: 15px; color: #666; font-size: 12px;">
            © ${new Date().getFullYear()} Job Partiel. Tous droits réservés.
          </div>
        </div>
        <style>
          @media only screen and (max-width: 600px) {
            h2 { font-size: 24px; }
            h3 { font-size: 18px; }
            p { font-size: 14px; }
            div[style*="display: flex"] { flex-direction: column; }
            div[style*="flex: 1"] { width: 100%; margin: 0 0 15px 0 !important; }
            div[style*="margin-right: 15px"] { margin: 0 0 10px 0 !important; }
          }
        </style>
      `
    };

    await queueEmail(mailOptions.to, mailOptions.subject, mailOptions.html);
    logSecurity.info(LogEventType.EMAIL_SENT, 'Email de nouvelle proposition envoyé', { email });
  } catch (error) {
    logSecurity.error(LogEventType.EMAIL_ERROR, 'Erreur lors de l\'envoi de l\'email de nouvelle proposition', { error, email });
    throw error;
  }
};

export const sendCounterOfferEmail = async (email: string, data: {
  missionTitle: string;
  ownerName: string;
  originalAmount: number;
  counterOfferAmount: number;
  message: string;
  missionId: string;
}) => {
  try {
    const mailOptions = {
      from: `"Job Partiel" <${process.env.SMTP_FROM}>` || '<EMAIL>',
      to: email,
      subject: `${EMAIL_ICONS.counterOffer} ${EMAIL_SUBJECTS.counterOffer}: "${data.missionTitle}"`,
      html: `
        <div style="font-family: Arial, sans-serif; width: 100%; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #FFF8F3 0%, #FFE4BA 100%); padding: 40px 0;">
          <div style="background-color: white; padding: 40px 20px; border-radius: 16px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
            <div style="text-align: center; margin-bottom: 30px;">
              <img src="${config.apiUrl}/api/storage-proxy/photo_profil/avatar/avatar-defaut-jobpartiel.jpg" alt="Job Partiel" style="max-width: 100%; height: auto;">
            </div>

            <div style="text-align: center; background-color: #FFF8F3; padding: 20px; border-radius: 12px; margin-bottom: 30px;">
              <div style="background-color: #FF7A35; display: inline-block; border-radius: 50%; width: 80px; height: 80px; line-height: 80px; text-align: center; margin-bottom: 15px;">
                <span style="font-size: 40px; color: white;">🔄</span>
              </div>
              <h2 style="color: #FF7A35; font-size: 28px; margin: 0 0 10px 0;">
                Contre-offre Reçue !
              </h2>
              <p style="color: #374151; font-size: 16px; margin: 0;">
                Pour la mission "${data.missionTitle}"
              </p>
            </div>

            <p style="line-height: 1.8; color: #374151; font-size: 16px;">
              Bonjour,
            </p>

            <p style="line-height: 1.8; color: #374151; font-size: 16px;">
              <strong>${data.ownerName}</strong> a fait une contre-offre pour votre proposition sur la mission "${data.missionTitle}". Voici les détails :
            </p>

            <div style="background-color: #FFF8F3; padding: 25px; border-radius: 12px; margin: 25px 0;">
              <div style="margin-top: 10px; background-color: white; padding: 20px; border-radius: 8px;">
                <div style="display: flex; justify-content: space-between; margin-bottom: 15px;">
                  <div style="color: #374151; flex: 1; text-align: center; padding: 15px; border-right: 1px dashed #E5E7EB;">
                    <div style="margin-bottom: 10px; background-color: #FFF8F3; border-radius: 50%; width: 50px; height: 50px; display: flex; align-items: center; justify-content: center; margin: 0 auto 10px auto;">
                      <span style="font-size: 24px;">📤</span>
                    </div>
                    <p style="margin: 0 0 5px 0;"><strong>Votre proposition :</strong></p>
                    <p style="font-size: 24px; color: #FF7A35; font-weight: bold; margin: 0;">${data.originalAmount}€</p>
                  </div>
                  <div style="color: #374151; flex: 1; text-align: center; padding: 15px;">
                    <div style="margin-bottom: 10px; background-color: #FFF8F3; border-radius: 50%; width: 50px; height: 50px; display: flex; align-items: center; justify-content: center; margin: 0 auto 10px auto;">
                      <span style="font-size: 24px;">📥</span>
                    </div>
                    <p style="margin: 0 0 5px 0;"><strong>Contre-offre :</strong></p>
                    <p style="font-size: 24px; color: #FF7A35; font-weight: bold; margin: 0;">${data.counterOfferAmount}€</p>
                  </div>
                </div>

                <div style="margin-top: 20px; border-top: 1px solid #E5E7EB; padding-top: 20px;">
                  <p style="color: #374151; margin: 0 0 10px 0;"><strong>Message du client :</strong></p>
                  <p style="color: #374151; font-style: italic; background-color: #FFF8F3; padding: 15px; border-radius: 8px; margin: 0;">
                    "${data.message}"
                  </p>
                </div>
              </div>
            </div>

            <div style="background-color: #FFF8F3; padding: 25px; border-radius: 12px; margin: 30px 0;">
              <h3 style="color: #FF7A35; margin: 0 0 15px 0; font-size: 20px;">
                <span style="margin-right: 10px;">💡</span>Comment répondre à cette contre-offre ?
              </h3>
              <ul style="color: #374151; font-size: 16px; line-height: 1.8; margin: 0; padding-left: 20px;">
                <li><strong>Accepter la contre-offre</strong> - Si le montant vous convient, vous pouvez l'accepter directement</li>
                <li><strong>Faire une nouvelle contre-proposition</strong> - Vous pouvez poursuivre la négociation</li>
                <li><strong>Refuser et maintenir votre offre</strong> - Si vous ne souhaitez pas modifier votre tarif</li>
                <li><strong>Demander plus de précisions</strong> - Si vous avez besoin de clarifications avant de décider</li>
              </ul>
            </div>

            <div style="background-color: #FFF8F3; padding: 25px; border-radius: 12px; margin: 30px 0;">
              <h3 style="color: #FF7A35; margin: 0 0 15px 0; font-size: 20px;">
                <span style="margin-right: 10px;">🔍</span>Conseils pour une négociation réussie
              </h3>
              <ul style="color: #374151; font-size: 16px; line-height: 1.8; margin: 0; padding-left: 20px;">
                <li><strong>Restez professionnel</strong> - Même en cas de désaccord, gardez une communication courtoise</li>
                <li><strong>Justifiez votre tarif</strong> - Expliquez la valeur de votre service et votre expertise</li>
                <li><strong>Proposez des options</strong> - Adaptez vos prestations en fonction du budget du client</li>
                <li><strong>Soyez flexible</strong> - Un bon compromis peut mener à une collaboration durable</li>
              </ul>
            </div>

            <div style="text-align: center; margin: 35px 0;">
              <a href="${process.env.FRONTEND_URL}/dashboard/missions/offres?tab=0&mission=${data.missionId}"
                 style="display: inline-block;
                        background-color: #FF7A35;
                        color: white;
                        padding: 16px 40px;
                        text-decoration: none;
                        border-radius: 12px;
                        font-weight: 600;
                        font-size: 18px;
                        transition: all 0.3s ease;
                        box-shadow: 0 4px 6px rgba(255, 122, 53, 0.2);">
                Répondre à la contre-offre
              </a>
            </div>

            <div style="
              margin: 30px auto;
              padding: 25px;
              background: linear-gradient(135deg, #FFF8F3 0%, #FFE4BA 100%);
              border-radius: 12px;
              max-width: 90%;
            ">
              <p style="
                color: #374151;
                font-size: 14px;
                margin-bottom: 15px;
                text-align: center;
              ">Si le bouton ne fonctionne pas, copiez et collez ce lien dans votre navigateur :</p>
              <p style="
                background-color: white;
                padding: 15px;
                border-radius: 8px;
                border: 2px solid #FFE4BA;
                font-family: monospace;
                font-size: 14px;
                color: #374151;
                word-break: break-all;
                margin: 0;
                text-align: center;
              ">${process.env.FRONTEND_URL}/dashboard/missions/offres?tab=0&mission=${data.missionId}</p>
            </div>

            <div style="text-align: center; margin-top: 30px; padding-top: 30px; border-top: 2px solid #FFE4BA;">
              <p style="color: #4B5563; font-size: 14px; margin-bottom: 10px;">
                Besoin d'aide pour négocier ? Notre équipe est là pour vous conseiller.
              </p>
              <a href="http://localhost:5173/dashboard/support/new"
                 style="color: #FF7A35;
                        text-decoration: none;
                        font-weight: 600;
                        font-size: 14px;">
                Contacter le support
              </a>
            </div>
          </div>
          <div style="text-align: center; margin-top: 15px; color: #666; font-size: 12px;">
            © ${new Date().getFullYear()} Job Partiel. Tous droits réservés.
          </div>
        </div>
        <style>
          @media only screen and (max-width: 600px) {
            h2 { font-size: 24px; }
            h3 { font-size: 18px; }
            p { font-size: 14px; }
            div[style*="display: flex"] { flex-direction: column; }
            div[style*="flex: 1"] { width: 100%; margin: 0 0 15px 0 !important; border-right: none !important; }
            div[style*="margin-right: 15px"] { margin: 0 0 10px 0 !important; }
          }
        </style>
      `
    };

    await queueEmail(mailOptions.to, mailOptions.subject, mailOptions.html);
    logSecurity.info(LogEventType.EMAIL_SENT, 'Email de contre-offre envoyé', { email });
  } catch (error) {
    logSecurity.error(LogEventType.EMAIL_ERROR, 'Erreur lors de l\'envoi de l\'email de contre-offre', { error, email });
    throw error;
  }
};

export const sendProposalAcceptedEmail = async (email: string, data: {
  missionTitle: string;
  ownerName: string;
  amount: number;
  missionId: string;
}) => {
  try {
    const baseUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
    const missionUrl = `${baseUrl}/dashboard/missions/${data.missionId}`;

    const mailOptions = {
      from: `"Job Partiel" <${process.env.SMTP_FROM}>` || '<EMAIL>',
      to: email,
      subject: `${EMAIL_ICONS.proposalAccepted} ${EMAIL_SUBJECTS.proposalAccepted}: "${data.missionTitle}"`,
      html: `
        <div style="font-family: 'Arial', sans-serif; width: 100%; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #FFF8F3 0%, #FFE4BA 100%); padding: 40px 0;">
          <div style="background-color: white; padding: 40px 20px; border-radius: 16px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
            <div style="text-align: center; margin-bottom: 30px;">
              <img src="${config.apiUrl}/api/storage-proxy/photo_profil/avatar/avatar-defaut-jobpartiel.jpg" alt="Job Partiel" style="max-width: 100%; height: auto;">
            </div>

            <div style="text-align: center; background-color: #FFF8F3; padding: 20px; border-radius: 12px; margin-bottom: 30px;">
              <div style="background-color: #FF7A35; display: inline-block; border-radius: 50%; width: 80px; height: 80px; line-height: 80px; text-align: center; margin-bottom: 15px;">
                <span style="font-size: 40px; color: white;">✅</span>
              </div>
              <h2 style="color: #FF7A35; font-size: 28px; margin: 0 0 10px 0;">
                Félicitations !
              </h2>
              <p style="color: #374151; font-size: 16px; margin: 0;">
                Votre proposition a été acceptée
              </p>
            </div>

            <p style="line-height: 1.8; color: #374151; font-size: 16px;">
              Bonjour,
            </p>

            <p style="line-height: 1.8; color: #374151; font-size: 16px;">
              Nous avons le plaisir de vous informer que <strong>${data.ownerName}</strong> a accepté votre proposition pour la mission suivante :
            </p>

            <div style="background-color: #FFF8F3; padding: 25px; border-radius: 12px; margin: 25px 0;">
              <div style="display: flex; align-items: center; margin-bottom: 20px;">
                <div style="background-color: white; border-radius: 50%; width: 50px; height: 50px; display: flex; align-items: center; justify-content: center; margin-right: 15px; flex-shrink: 0;">
                  <span style="font-size: 24px;">📋</span>
                </div>
                <div>
                  <h3 style="color: #FF7A35; margin: 0 0 5px 0; font-size: 20px;">
                    ${data.missionTitle}
                  </h3>
                </div>
              </div>

              <div style="background-color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                <div style="display: flex; align-items: center; margin-bottom: 10px;">
                  <div style="margin-right: 10px; background-color: #FFF8F3; border-radius: 50%; width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;">
                    <span style="font-size: 20px;">💰</span>
                  </div>
                  <div>
                    <p style="margin: 0; color: #6B7280; font-size: 14px;">Montant accepté</p>
                    <p style="margin: 5px 0 0 0; color: #374151; font-weight: bold; font-size: 24px;">
                      ${data.amount}€
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div style="background-color: #FFF8F3; padding: 25px; border-radius: 12px; margin: 30px 0;">
              <h3 style="color: #FF7A35; margin: 0 0 15px 0; font-size: 20px;">
                <span style="margin-right: 10px;">📝</span>Prochaines étapes
              </h3>
              <ol style="color: #374151; font-size: 16px; line-height: 1.8; margin: 0; padding-left: 20px;">
                <li><strong>Contactez le client</strong> - Utilisez la messagerie de la plateforme pour discuter des détails</li>
                <li><strong>Confirmez les attentes</strong> - Assurez-vous de bien comprendre ce qui est attendu</li>
                <li><strong>Planifiez l'intervention</strong> - Fixez une date et une heure précises</li>
                <li><strong>Préparez-vous</strong> - Rassemblez tout le matériel nécessaire avant l'intervention</li>
                <li><strong>Réalisez la mission</strong> - Effectuez le travail avec professionnalisme</li>
                <li><strong>Demandez un avis</strong> - Une fois la mission terminée, n'hésitez pas à demander un avis</li>
              </ol>
            </div>

            <div style="background-color: #FFF8F3; padding: 25px; border-radius: 12px; margin: 30px 0;">
              <h3 style="color: #FF7A35; margin: 0 0 15px 0; font-size: 20px;">
                <span style="margin-right: 10px;">💡</span>Conseils pour réussir votre mission
              </h3>
              <ul style="color: #374151; font-size: 16px; line-height: 1.8; margin: 0; padding-left: 20px;">
                <li><strong>Soyez ponctuel</strong> - Arrivez à l'heure convenue, voire un peu en avance</li>
                <li><strong>Communiquez clairement</strong> - Informez le client de tout imprévu ou changement</li>
                <li><strong>Soyez professionnel</strong> - Portez une tenue adaptée et restez courtois</li>
                <li><strong>Documentez votre travail</strong> - Prenez des photos avant/après si approprié</li>
                <li><strong>Dépassez les attentes</strong> - Un petit geste supplémentaire peut faire la différence</li>
              </ul>
            </div>

            <div style="text-align: center; margin: 35px 0;">
              <a href="${baseUrl}/dashboard/missions/offres?tab=0&mission=${data.missionId}"
                 style="display: inline-block;
                        background-color: #FF7A35;
                        color: white;
                        padding: 16px 40px;
                        text-decoration: none;
                        border-radius: 12px;
                        font-weight: 600;
                        font-size: 18px;
                        transition: all 0.3s ease;
                        box-shadow: 0 4px 6px rgba(255, 122, 53, 0.2);">
                Voir les détails de la mission
              </a>
            </div>

            <div style="
              margin: 30px auto;
              padding: 25px;
              background: linear-gradient(135deg, #FFF8F3 0%, #FFE4BA 100%);
              border-radius: 12px;
              max-width: 90%;
            ">
              <p style="
                color: #374151;
                font-size: 14px;
                margin-bottom: 15px;
                text-align: center;
              ">Si le bouton ne fonctionne pas, copiez et collez ce lien dans votre navigateur :</p>
              <p style="
                background-color: white;
                padding: 15px;
                border-radius: 8px;
                border: 2px solid #FFE4BA;
                font-family: monospace;
                font-size: 14px;
                color: #374151;
                word-break: break-all;
                margin: 0;
                text-align: center;
              ">${baseUrl}/dashboard/missions/offres?tab=0&mission=${data.missionId}</p>
            </div>

            <div style="text-align: center; margin-top: 30px; padding-top: 30px; border-top: 2px solid #FFE4BA;">
              <p style="color: #4B5563; font-size: 14px; margin-bottom: 10px;">
                Besoin d'aide pour préparer votre mission ? Notre équipe est là pour vous guider.
              </p>
              <a href="http://localhost:5173/dashboard/support/new"
                 style="color: #FF7A35;
                        text-decoration: none;
                        font-weight: 600;
                        font-size: 14px;">
                Contacter le support
              </a>
            </div>
          </div>
          <div style="text-align: center; margin-top: 15px; color: #666; font-size: 12px;">
            © ${new Date().getFullYear()} Job Partiel. Tous droits réservés.
          </div>
        </div>
        <style>
          @media only screen and (max-width: 600px) {
            h2 { font-size: 24px; }
            h3 { font-size: 18px; }
            p { font-size: 14px; }
            div[style*="display: flex"] { flex-direction: column; }
            div[style*="flex: 1"] { width: 100%; margin: 0 0 15px 0 !important; }
            div[style*="margin-right: 15px"] { margin: 0 0 10px 0 !important; }
          }
        </style>
      `
    };

    await queueEmail(mailOptions.to, mailOptions.subject, mailOptions.html);
    logSecurity.info(LogEventType.EMAIL_SENT, 'Email d\'acceptation de proposition envoyé', { email });
  } catch (error) {
    logSecurity.error(LogEventType.EMAIL_ERROR, 'Erreur lors de l\'envoi de l\'email d\'acceptation de proposition', { error, email });
    throw error;
  }
};

export const sendProposalRejectedEmail = async (email: string, data: {
  missionTitle: string;
  ownerName: string;
  missionId: string;
}) => {
  try {
    const baseUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
    const missionUrl = `${baseUrl}/dashboard/missions/${data.missionId}`;

    const mailOptions = {
      from: `"Job Partiel" <${process.env.SMTP_FROM}>` || '<EMAIL>',
      to: email,
      subject: `${EMAIL_ICONS.proposalRejected} Votre proposition pour la mission "${data.missionTitle}" a été refusée`,
      html: `
        <div style="font-family: Arial, sans-serif; width: 100%; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #FFF8F3 0%, #FFE4BA 100%); padding: 40px 0;">
          <div style="background-color: white; padding: 40px 20px; border-radius: 16px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
            <div style="text-align: center; margin-bottom: 30px;">
              <img src="${baseUrl}/logo.png" alt="JobPartiel Logo" style="max-width: 150px; height: auto;">
            </div>

            <div style="text-align: center; margin-bottom: 30px;">
              <div style="display: inline-block; background-color: #FFF8F3; border-radius: 50%; width: 80px; height: 80px; line-height: 80px; text-align: center; margin-bottom: 15px;">
                <span style="font-size: 40px;">❌</span>
              </div>
              <h2 style="color: #FF7A35; font-size: 28px; margin: 10px 0;">Proposition refusée</h2>
              <p style="color: #4B5563; font-size: 18px; margin: 0;">Nous sommes désolés, mais votre offre n'a pas été retenue</p>
            </div>

            <div style="background-color: #FFF8F3; padding: 25px; border-radius: 12px; margin: 30px 0;">
              <p style="color: #374151; font-size: 16px; line-height: 1.8; margin-bottom: 20px;">
                Bonjour,
              </p>
              <p style="color: #374151; font-size: 16px; line-height: 1.8; margin-bottom: 20px;">
                Nous sommes désolés de vous informer que <strong>${data.ownerName}</strong> a refusé votre proposition pour la mission suivante :
              </p>

              <div style="background-color: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <h3 style="color: #FF7A35; margin: 0 0 10px 0; font-size: 18px;">
                  <span style="margin-right: 10px;">📋</span>${data.missionTitle}
                </h3>
              </div>
            </div>

            <div style="background-color: #FFF8F3; padding: 25px; border-radius: 12px; margin: 30px 0;">
              <h3 style="color: #FF7A35; margin: 0 0 15px 0; font-size: 20px;">
                <span style="margin-right: 10px;">💡</span>Conseils pour vos futures propositions
              </h3>
              <ul style="color: #374151; font-size: 16px; line-height: 1.8; margin: 0; padding-left: 20px;">
                <li><strong>Personnalisez vos offres</strong> - Adaptez votre proposition aux besoins spécifiques de chaque mission</li>
                <li><strong>Mettez en avant vos compétences</strong> - Expliquez pourquoi vous êtes la personne idéale pour cette mission</li>
                <li><strong>Soyez réaliste sur les tarifs</strong> - Proposez un prix compétitif mais qui reflète la qualité de votre travail</li>
                <li><strong>Répondez rapidement</strong> - Les clients apprécient les prestataires réactifs</li>
                <li><strong>Complétez votre profil</strong> - Un profil détaillé avec des avis positifs augmente vos chances</li>
              </ul>
            </div>

            <div style="background-color: #FFF8F3; padding: 25px; border-radius: 12px; margin: 30px 0;">
              <h3 style="color: #FF7A35; margin: 0 0 15px 0; font-size: 20px;">
                <span style="margin-right: 10px;">🔍</span>Et maintenant ?
              </h3>
              <p style="color: #374151; font-size: 16px; line-height: 1.8; margin-bottom: 15px;">
                Ne vous découragez pas ! Il y a de nombreuses autres opportunités qui correspondent à vos compétences sur JobPartiel.
              </p>
              <p style="color: #374151; font-size: 16px; line-height: 1.8; margin-bottom: 0;">
                Continuez à proposer vos services et à améliorer votre profil pour augmenter vos chances de succès.
              </p>
            </div>

            <div style="text-align: center; margin: 35px 0;">
              <a href="${baseUrl}/dashboard/missions/"
                 style="display: inline-block;
                        background-color: #FF7A35;
                        color: white;
                        padding: 16px 40px;
                        text-decoration: none;
                        border-radius: 12px;
                        font-weight: 600;
                        font-size: 18px;
                        transition: all 0.3s ease;
                        box-shadow: 0 4px 6px rgba(255, 122, 53, 0.2);">
                Découvrir d'autres missions
              </a>
            </div>

            <div style="
              margin: 30px auto;
              padding: 25px;
              background: linear-gradient(135deg, #FFF8F3 0%, #FFE4BA 100%);
              border-radius: 12px;
              max-width: 90%;
            ">
              <p style="
                color: #374151;
                font-size: 14px;
                margin-bottom: 15px;
                text-align: center;
              ">Si le bouton ne fonctionne pas, copiez et collez ce lien dans votre navigateur :</p>
              <p style="
                background-color: white;
                padding: 15px;
                border-radius: 8px;
                border: 2px solid #FFE4BA;
                font-family: monospace;
                font-size: 14px;
                color: #374151;
                word-break: break-all;
                margin: 0;
                text-align: center;
              ">${baseUrl}/dashboard/missions/offres?tab=0&mission=${data.missionId}</p>
            </div>

            <div style="text-align: center; margin-top: 30px; padding-top: 30px; border-top: 2px solid #FFE4BA;">
              <p style="color: #4B5563; font-size: 14px; margin-bottom: 10px;">
                Besoin d'aide pour améliorer vos propositions ? Notre équipe est là pour vous guider.
              </p>
              <a href="http://localhost:5173/dashboard/support/new"
                 style="color: #FF7A35;
                        text-decoration: none;
                        font-weight: 600;
                        font-size: 14px;">
                Contacter le support
              </a>
            </div>
          </div>
          <div style="text-align: center; margin-top: 15px; color: #666; font-size: 12px;">
            © ${new Date().getFullYear()} Job Partiel. Tous droits réservés.
          </div>
        </div>
        <style>
          @media only screen and (max-width: 600px) {
            h2 { font-size: 24px; }
            h3 { font-size: 18px; }
            p { font-size: 14px; }
            div[style*="padding: 40px"] { padding: 20px !important; }
            a[style*="padding: 16px 40px"] { padding: 12px 25px !important; font-size: 16px !important; }
          }
        </style>
      `
    };

    await queueEmail(mailOptions.to, mailOptions.subject, mailOptions.html);
    logSecurity.info(LogEventType.EMAIL_SENT, 'Email de refus de proposition envoyé', { email });
  } catch (error) {
    logSecurity.error(LogEventType.EMAIL_ERROR, 'Erreur lors de l\'envoi de l\'email de refus de proposition', { error, email });
    throw error;
  }
};

export const sendJobbeurCounterOfferEmail = async (email: string, data: {
  missionTitle: string;
  jobbeurName: string;
  originalAmount: number;
  counterOfferAmount: number;
  message: string;
  missionId: string;
  proposalId: string;
}) => {
  try {
    const baseUrl = process.env.FRONTEND_URL || 'http://localhost:5173';

    const mailOptions = {
      from: `"Job Partiel" <${process.env.SMTP_FROM}>` || '<EMAIL>',
      to: email,
      subject: `${EMAIL_ICONS.jobbeurCounterOffer} Nouvelle contre-offre pour la mission "${data.missionTitle}"`,
      html: `
        <div style="font-family: Arial, sans-serif; width: 100%; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #FFF8F3 0%, #FFE4BA 100%); padding: 40px 0;">
          <div style="background-color: white; padding: 40px 20px; border-radius: 16px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
            <div style="text-align: center; margin-bottom: 30px;">
              <img src="${baseUrl}/logo.png" alt="JobPartiel Logo" style="max-width: 150px; height: auto;">
            </div>

            <div style="text-align: center; margin-bottom: 30px;">
              <div style="display: inline-block; background-color: #FFF8F3; border-radius: 50%; width: 80px; height: 80px; line-height: 80px; text-align: center; margin-bottom: 15px;">
                <span style="font-size: 40px;">🔄</span>
              </div>
              <h2 style="color: #FF7A35; font-size: 28px; margin: 10px 0;">Nouvelle contre-offre</h2>
              <p style="color: #4B5563; font-size: 18px; margin: 0;">Une contre-offre a été proposée pour votre mission</p>
            </div>

            <div style="background-color: #FFF8F3; padding: 25px; border-radius: 12px; margin: 30px 0;">
              <p style="color: #374151; font-size: 16px; line-height: 1.8; margin-bottom: 20px;">
                <strong>${data.jobbeurName}</strong> a fait une nouvelle contre-offre pour la mission :
              </p>

              <div style="background-color: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <h3 style="color: #FF7A35; margin: 0 0 15px 0; font-size: 18px;">
                  <span style="margin-right: 10px;">📋</span>${data.missionTitle}
                </h3>
              </div>

              <div style="background-color: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                  <div style="flex: 1; text-align: center; padding: 15px; border-right: 2px dotted #FFE4BA;">
                    <p style="color: #4B5563; font-size: 14px; margin: 0 0 5px 0;">Votre contre-offre</p>
                    <div style="font-size: 16px; color: #FF7A35; margin-bottom: 5px;">📤</div>
                    <p style="font-size: 22px; font-weight: bold; color: #374151; margin: 0;">${data.originalAmount}€</p>
                  </div>
                  <div style="flex: 1; text-align: center; padding: 15px;">
                    <p style="color: #4B5563; font-size: 14px; margin: 0 0 5px 0;">Nouvelle contre-offre</p>
                    <div style="font-size: 16px; color: #FF7A35; margin-bottom: 5px;">📥</div>
                    <p style="font-size: 22px; font-weight: bold; color: #FF7A35; margin: 0;">${data.counterOfferAmount}€</p>
                  </div>
                </div>

                <div style="margin-top: 20px; border-top: 1px solid #FFE4BA; padding-top: 20px;">
                  <p style="color: #374151; font-weight: bold; margin: 0 0 10px 0;">Message du jobbeur :</p>
                  <div style="color: #374151; font-style: italic; background-color: #FFF8F3; padding: 15px; border-radius: 8px; border-left: 3px solid #FF7A35;">
                    ${data.message}
                  </div>
                </div>
              </div>
            </div>

            <div style="background-color: #FFF8F3; padding: 25px; border-radius: 12px; margin: 30px 0;">
              <h3 style="color: #FF7A35; margin: 0 0 15px 0; font-size: 20px;">
                <span style="margin-right: 10px;">💡</span>Comment répondre à cette contre-offre ?
              </h3>
              <ul style="color: #374151; font-size: 16px; line-height: 1.8; margin: 0; padding-left: 20px;">
                <li><strong>Accepter</strong> - Si le montant vous convient, vous pouvez accepter directement</li>
                <li><strong>Faire une nouvelle contre-offre</strong> - Proposez un montant intermédiaire</li>
                <li><strong>Refuser</strong> - Si le montant est trop bas, vous pouvez refuser la proposition</li>
              </ul>
            </div>

            <div style="background-color: #FFF8F3; padding: 25px; border-radius: 12px; margin: 30px 0;">
              <h3 style="color: #FF7A35; margin: 0 0 15px 0; font-size: 20px;">
                <span style="margin-right: 10px;">🔍</span>Conseils pour une négociation réussie
              </h3>
              <ul style="color: #374151; font-size: 16px; line-height: 1.8; margin: 0; padding-left: 20px;">
                <li><strong>Restez ouvert</strong> - La négociation est un processus normal</li>
                <li><strong>Considérez la valeur globale</strong> - Pensez à la qualité du jobbeur et à ses avis</li>
                <li><strong>Communiquez clairement</strong> - Expliquez vos attentes et contraintes</li>
                <li><strong>Soyez réactif</strong> - Une réponse rapide montre votre intérêt</li>
              </ul>
            </div>

            <div style="text-align: center; margin: 35px 0;">
              <a href="${baseUrl}/dashboard/missions/offres?tab=1&mission=${data.missionId}"
                 style="display: inline-block;
                        background-color: #FF7A35;
                        color: white;
                        padding: 16px 40px;
                        text-decoration: none;
                        border-radius: 12px;
                        font-weight: 600;
                        font-size: 18px;
                        transition: all 0.3s ease;
                        box-shadow: 0 4px 6px rgba(255, 122, 53, 0.2);">
                Répondre à la contre-offre
              </a>
            </div>

            <div style="
              margin: 30px auto;
              padding: 25px;
              background: linear-gradient(135deg, #FFF8F3 0%, #FFE4BA 100%);
              border-radius: 12px;
              max-width: 90%;
            ">
              <p style="
                color: #374151;
                font-size: 14px;
                margin-bottom: 15px;
                text-align: center;
              ">Si le bouton ne fonctionne pas, copiez et collez ce lien dans votre navigateur :</p>
              <p style="
                background-color: white;
                padding: 15px;
                border-radius: 8px;
                border: 2px solid #FFE4BA;
                font-family: monospace;
                font-size: 14px;
                color: #374151;
                word-break: break-all;
                margin: 0;
                text-align: center;
              ">${baseUrl}/dashboard/missions/offres?tab=1&mission=${data.missionId}</p>
            </div>

            <div style="text-align: center; margin-top: 30px; padding-top: 30px; border-top: 2px solid #FFE4BA;">
              <p style="color: #4B5563; font-size: 14px; margin-bottom: 10px;">
                Besoin d'aide pour gérer vos négociations ? Notre équipe est là pour vous guider.
              </p>
              <a href="http://localhost:5173/dashboard/support/new"
                 style="color: #FF7A35;
                        text-decoration: none;
                        font-weight: 600;
                        font-size: 14px;">
                Contacter le support
              </a>
            </div>
          </div>
          <div style="text-align: center; margin-top: 15px; color: #666; font-size: 12px;">
            © ${new Date().getFullYear()} Job Partiel. Tous droits réservés.
          </div>
        </div>
        <style>
          @media only screen and (max-width: 600px) {
            h2 { font-size: 24px; }
            h3 { font-size: 18px; }
            p { font-size: 14px; }
            div[style*="padding: 40px"] { padding: 20px !important; }
            div[style*="display: flex"] { flex-direction: column; }
            div[style*="border-right: 2px dotted"] { border-right: none !important; border-bottom: 2px dotted #FFE4BA; padding-bottom: 15px; margin-bottom: 15px; }
            a[style*="padding: 16px 40px"] { padding: 12px 25px !important; font-size: 16px !important; }
          }
        </style>
      `
    };

    await queueEmail(mailOptions.to, mailOptions.subject, mailOptions.html);
    logSecurity.info(LogEventType.EMAIL_SENT, 'Email de contre-offre jobbeur envoyé', { email });
  } catch (error) {
    logSecurity.error(LogEventType.EMAIL_ERROR, 'Erreur lors de l\'envoi de l\'email de contre-offre jobbeur', { error, email });
    throw error;
  }
};

export const sendContactInfoEmail = async (email: string, data: {
  missionTitle: string;
  clientName: string;
  nom_prenom?: string;
  email?: string;
  telephone?: string;
  adresse?: string;
  message: string;
  missionId: string;
}) => {
  try {
    // Vérification du rate limit
    checkRateLimit(email);

    const mailOptions = {
      from: `"Job Partiel" <${process.env.SMTP_FROM}>` || '<EMAIL>',
      to: email,
      subject: `${EMAIL_ICONS.contactInfo} Informations de contact partagées pour la mission "${data.missionTitle}"`,
      html: `
      <div style="font-family: 'Arial', sans-serif; width: 100%; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #FFF8F3 0%, #FFE4BA 100%); padding: 40px 0;">
        <div style="background-color: white; padding: 40px 20px; border-radius: 16px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
          <div style="text-align: center; margin-bottom: 30px;">
            <img src="${config.apiUrl}/api/storage-proxy/photo_profil/avatar/avatar-defaut-jobpartiel.jpg" alt="Job Partiel" style="max-width: 150px; height: auto;">
          </div>

          <h1 style="color: #FF6B2C; text-align: center; margin-bottom: 30px; font-size: 24px;">Informations de contact reçues</h1>

          <div style="background-color: #FFF8F3; border-left: 4px solid #FF6B2C; padding: 15px; margin-bottom: 25px; border-radius: 0 8px 8px 0;">
            <p style="margin: 0; color: #333; font-size: 16px;">${data.message}</p>
          </div>

          <div style="margin-bottom: 30px;">
            <h2 style="color: #FF6B2C; font-size: 20px; margin-bottom: 15px;">Coordonnées du client :</h2>
            <ul style="list-style-type: none; padding: 0; margin: 0;">
              <li style="padding: 10px 0; border-bottom: 1px solid #eee;">
                <strong style="color: #FF6B2C;">Nom et prénom :</strong> ${data.nom_prenom || 'Non partagés'}
              </li>
              <li style="padding: 10px 0; border-bottom: 1px solid #eee;">
                <strong style="color: #FF6B2C;">Email :</strong> ${data.email || 'Non partagé'}
              </li>
              <li style="padding: 10px 0; border-bottom: 1px solid #eee;">
                <strong style="color: #FF6B2C;">Téléphone :</strong> ${data.telephone || 'Non partagé'}
              </li>
              <li style="padding: 10px 0;">
                <strong style="color: #FF6B2C;">Adresse :</strong> ${data.adresse || 'Non partagée'}
              </li>
            </ul>
          </div>

          <div style="background-color: #FFF8F3; padding: 20px; border-radius: 8px; margin-bottom: 30px; border: 1px solid #FFE4BA;">
            <h3 style="color: #FF6B2C; font-size: 18px; margin-top: 0; margin-bottom: 15px;">Conseils pour une collaboration réussie</h3>

            <div style="margin-bottom: 15px;">
              <h4 style="color: #FF6B2C; font-size: 16px; margin-bottom: 5px;">🤝 Bienveillance</h4>
              <p style="margin: 0; color: #333; font-size: 14px;">Privilégiez une communication respectueuse et bienveillante. Une bonne relation de confiance est la clé d'une collaboration réussie.</p>
            </div>

            <div style="margin-bottom: 15px;">
              <h4 style="color: #FF6B2C; font-size: 16px; margin-bottom: 5px;">🔒 Sécurité</h4>
              <p style="margin: 0; color: #333; font-size: 14px;">Lors de votre premier contact, convenez d'un lieu public pour vous rencontrer. Vérifiez les avis et le profil du client avant de vous rendre à son domicile.</p>
            </div>

            <div style="margin-bottom: 15px;">
              <h4 style="color: #FF6B2C; font-size: 16px; margin-bottom: 5px;">📝 Clarté</h4>
              <p style="margin: 0; color: #333; font-size: 14px;">Confirmez par écrit les détails de la mission (horaires, tarifs, tâches) pour éviter tout malentendu.</p>
            </div>

            <div>
              <h4 style="color: #FF6B2C; font-size: 16px; margin-bottom: 5px;">⏱️ Ponctualité</h4>
              <p style="margin: 0; color: #333; font-size: 14px;">Respectez les horaires convenus et prévenez en cas de retard ou d'imprévu. La ponctualité est un signe de professionnalisme.</p>
            </div>

            <div style="margin-top: 15px; margin-bottom: 15px;">
              <h4 style="color: #FF6B2C; font-size: 16px; margin-bottom: 5px;">💰 Paiement sécurisé</h4>
              <p style="margin: 0; color: #333; font-size: 14px;">Privilégiez les échanges/trocs via la plateforme JobPartiel avec vos Jobi pour bénéficier de notre protection. Évitez les transactions en espèces pour les premières missions.</p>
            </div>

            <div style="margin-top: 15px;">
              <h4 style="color: #FF6B2C; font-size: 16px; margin-bottom: 5px;">🛡️ Assistance</h4>
              <p style="margin: 0; color: #333; font-size: 14px;">En cas de problème ou de litige, contactez notre équipe support via la messagerie de la plateforme. Nous sommes là pour vous aider à résoudre toute situation délicate.</p>
            </div>
          </div>

          <div style="background-color: #E6F7FF; padding: 15px; border-radius: 8px; margin: 30px 0; border: 1px solid #91D5FF;">
            <h3 style="color: #1890FF; font-size: 16px; margin-top: 0; margin-bottom: 10px;">💡 Astuce pour réussir votre mission</h3>
            <p style="margin: 0; color: #333; font-size: 14px;">Prenez quelques photos avant et après la réalisation de votre mission. Cela vous permettra de mettre en valeur votre travail et d'enrichir votre profil avec des réalisations concrètes. Les clients apprécient de voir des exemples de travaux réussis !</p>
          </div>

          <div style="text-align: center; margin-top: 30px;">
            <a href="${process.env.FRONTEND_URL}/dashboard/missions/${data.missionId}" style="background-color: #FF6B2C; color: white; padding: 12px 25px; text-decoration: none; border-radius: 8px; font-weight: bold; display: inline-block;">Voir la mission</a>
          </div>

          <div style="margin-top: 40px; text-align: center; color: #666; font-size: 14px;">
            <p>Cet email a été envoyé par Job Partiel.</p>
            <p>© ${new Date().getFullYear()} Job Partiel. Tous droits réservés.</p>
          </div>
        </div>
      </div>
      `
    };

    await queueEmail(mailOptions.to, mailOptions.subject, mailOptions.html);
  } catch (error) {
    if (error instanceof EmailServiceError) {
      throw error;
    }
    throw new EmailServiceError(
      `Erreur lors de l'envoi de l'email d'informations de contact : ${error instanceof Error ? error.message : String(error)}`,
      'CONTACT_INFO_EMAIL_ERROR',
      { error }
    );
  }
};

export const sendMissionCancelledEmail = async (email: string, data: {
  missionTitle: string;
  missionId: string;
}) => {
  try {
    const baseUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
    const missionUrl = `${baseUrl}/dashboard/missions/${data.missionId}`;

    const mailOptions = {
      from: `"Job Partiel" <${process.env.SMTP_FROM}>` || '<EMAIL>',
      to: email,
      subject: `${EMAIL_ICONS.missionCancelled} La mission "${data.missionTitle}" a été annulée`,
      html: `
        <div style="font-family: Arial, sans-serif; width: 100%; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #FFF8F3 0%, #FFE4BA 100%); padding: 40px 0;">
          <div style="background-color: white; padding: 40px; border-radius: 16px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
            <div style="text-align: center; margin-bottom: 30px;">
              <img src="${baseUrl}/logo.png" alt="JobPartiel Logo" style="max-width: 150px; height: auto;">
            </div>

            <div style="text-align: center; margin-bottom: 30px;">
              <div style="display: inline-block; background-color: #FFF8F3; border-radius: 50%; width: 80px; height: 80px; line-height: 80px; text-align: center; margin-bottom: 15px;">
                <span style="font-size: 40px;">🚫</span>
              </div>
              <h2 style="color: #FF7A35; font-size: 28px; margin: 10px 0;">Mission annulée</h2>
              <p style="color: #4B5563; font-size: 18px; margin: 0;">La mission pour laquelle vous avez fait une offre a été annulée</p>
            </div>

            <div style="background-color: #FFF8F3; padding: 25px; border-radius: 12px; margin: 30px 0;">
              <p style="color: #374151; font-size: 16px; line-height: 1.8; margin-bottom: 20px;">
                Bonjour,
              </p>
              <p style="color: #374151; font-size: 16px; line-height: 1.8; margin-bottom: 20px;">
                Nous vous informons que la mission <strong>"${data.missionTitle}"</strong> pour laquelle vous avez fait une offre a été annulée par le client.
              </p>
              <p style="color: #374151; font-size: 16px; line-height: 1.8; margin-bottom: 20px;">
                Votre candidature a donc été automatiquement refusée. Nous vous encourageons à consulter d'autres missions disponibles sur notre plateforme.
              </p>
            </div>

            <div style="text-align: center; margin: 35px 0;">
              <a href="${baseUrl}/dashboard/missions/"
                 style="display: inline-block;
                        background-color: #FF7A35;
                        color: white;
                        padding: 16px 40px;
                        text-decoration: none;
                        border-radius: 12px;
                        font-weight: 600;
                        font-size: 18px;
                        transition: all 0.3s ease;
                        box-shadow: 0 4px 6px rgba(255, 122, 53, 0.2);">
                Voir les missions disponibles
              </a>
            </div>

            <div style="text-align: center; color: #6B7280; font-size: 14px; margin-top: 40px;">
              <p>Si vous avez des questions, n'hésitez pas à contacter notre équipe de support.</p>
              <p>© ${new Date().getFullYear()} Job Partiel. Tous droits réservés.</p>
            </div>
          </div>
        </div>
      `
    };

    await queueEmail(mailOptions.to, mailOptions.subject, mailOptions.html);
    logSecurity.info(LogEventType.EMAIL_SENT, 'Email de mission annulée envoyé', { to: email });
  } catch (error) {
    if (error instanceof EmailServiceError) {
      throw error;
    }

    const errorMessage = error instanceof Error ? error.message : 'Une erreur inconnue est survenue';
    throw new EmailServiceError(
      EMAIL_ERRORS.SMTP_ERROR.message(errorMessage),
      EMAIL_ERRORS.SMTP_ERROR.code,
      { originalError: errorMessage }
    );
  }
};

// Envoyer un email d'expiration d'offre
export const sendOfferExpirationEmail = async (email: string, data: {
  missionTitle: string;
  missionId: string;
  offerType: string;
  reason: string;
  senderName?: string;
  amount?: number;
  proposalDate?: string;
  message?: string;
  missionOwnerName?: string;
}) => {
  try {
    const baseUrl = process.env.FRONTEND_URL || 'http://localhost:5173';

    // Log des données reçues pour le débogage
    logSecurity.info(LogEventType.EMAIL_SENT, 'Données reçues pour l\'email d\'expiration', {
      email,
      data: JSON.stringify(data)
    });

    // Valeurs par défaut pour les champs optionnels
    const senderName = data.senderName || 'Système';
    const amount = data.amount || 0;
    const proposalDate = data.proposalDate || new Date().toISOString();
    const message = data.message || 'Aucun message';

    // Formater la date
    const formattedDate = new Date(proposalDate).toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });

    const mailOptions = {
      from: `"Job Partiel" <${process.env.SMTP_FROM}>` || '<EMAIL>',
      to: email,
      subject: `${EMAIL_ICONS.offerExpiration} Offre expirée pour la mission "${data.missionTitle}"`,
      html: `
        <div style="font-family: Arial, sans-serif; width: 100%; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #FFF8F3 0%, #FFE4BA 100%); padding: 40px 0;">
          <div style="background-color: white; padding: 40px; border-radius: 16px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
            <div style="text-align: center; margin-bottom: 30px;">
              <img src="${baseUrl}/logo.png" alt="JobPartiel Logo" style="max-width: 150px; height: auto;">
            </div>

            <div style="text-align: center; margin-bottom: 30px;">
              <div style="display: inline-block; background-color: #FFF8F3; border-radius: 50%; width: 80px; height: 80px; line-height: 80px; text-align: center; margin-bottom: 15px;">
                <span style="font-size: 40px;">⏱️</span>
              </div>
              <h2 style="color: #FF7A35; font-size: 28px; margin: 10px 0;">Offre expirée</h2>
              <p style="color: #4B5563; font-size: 18px; margin: 0;">Le délai de réponse de 24h est dépassé</p>
            </div>

            <div style="background-color: #FFF8F3; padding: 25px; border-radius: 12px; margin: 30px 0;">
              <p style="color: #374151; font-size: 16px; line-height: 1.8; margin-bottom: 20px;">
                Bonjour,
              </p>
              <p style="color: #374151; font-size: 16px; line-height: 1.8; margin-bottom: 20px;">
                Nous vous informons que ${data.offerType} pour la mission <strong>"${data.missionTitle}"</strong> a été automatiquement ${data.reason} car le délai de réponse de 24h est dépassé.
              </p>

              <div style="background-color: white; border-radius: 8px; padding: 20px; margin: 25px 0; border-left: 4px solid #FF7A35;">
                <p style="color: #4B5563; font-size: 14px; margin: 0 0 10px 0;">Détails de l'offre :</p>
                <p style="color: #374151; font-size: 15px; margin: 5px 0;"><strong>Proposée par :</strong> ${senderName}</p>
                <p style="color: #374151; font-size: 15px; margin: 5px 0;"><strong>Montant :</strong> ${amount.toFixed(2)}€</p>
                <p style="color: #374151; font-size: 15px; margin: 5px 0;"><strong>Date de proposition :</strong> ${formattedDate}</p>
                <p style="color: #374151; font-size: 15px; margin: 10px 0 5px 0;"><strong>Message :</strong></p>
                <div style="background-color: #F9FAFB; padding: 15px; border-radius: 6px; color: #4B5563; font-size: 14px; line-height: 1.6;">
                  ${message}
                </div>
              </div>

              <p style="color: #374151; font-size: 16px; line-height: 1.8; margin-bottom: 20px;">
                Pour rappel, toutes les offres et contre-offres doivent recevoir une réponse dans un délai de 24 heures. Passé ce délai, elles sont automatiquement refusées.
              </p>

              <div style="background-color: #FEE2E2; border-left: 4px solid #B91C1C; padding: 18px; border-radius: 8px; margin: 30px 0 0 0;">
                <p style="color: #B91C1C; font-size: 15px; margin: 0; font-weight: bold;">
                  ⚠️ Important :
                </p>
                <p style="color: #B91C1C; font-size: 15px; margin: 8px 0 0 0;">
                  En l'absence de réponse dans le délai imparti, un avis négatif automatique a été publié sur le profil ${data.missionOwnerName ? `de ${data.missionOwnerName}` : "du propriétaire de la mission"}.
                </p>
              </div>
            </div>

            <div style="text-align: center; margin: 35px 0;">
              <a href="${baseUrl}/dashboard/missions/offres"
                 style="display: inline-block;
                        background-color: #FF7A35;
                        color: white;
                        padding: 16px 40px;
                        text-decoration: none;
                        border-radius: 12px;
                        font-weight: 600;
                        font-size: 18px;
                        transition: all 0.3s ease;
                        box-shadow: 0 4px 6px rgba(255, 122, 53, 0.2);">
                Voir mes offres
              </a>
            </div>

            <div style="text-align: center; color: #6B7280; font-size: 14px; margin-top: 40px;">
              <p>Si vous avez des questions, n'hésitez pas à contacter notre équipe de support.</p>
              <p>© ${new Date().getFullYear()} Job Partiel. Tous droits réservés.</p>
            </div>
          </div>
        </div>
      `
    };

    await queueEmail(mailOptions.to, mailOptions.subject, mailOptions.html);
    logSecurity.info(LogEventType.EMAIL_SENT, 'Email d\'expiration d\'offre envoyé', { to: email });
  } catch (error) {
    if (error instanceof EmailServiceError) {
      throw error;
    }

    const errorMessage = error instanceof Error ? error.message : 'Une erreur inconnue est survenue';
    throw new EmailServiceError(
      EMAIL_ERRORS.SMTP_ERROR.message(errorMessage),
      EMAIL_ERRORS.SMTP_ERROR.code,
      { originalError: errorMessage }
    );
  }
};

// Envoyer un email de rappel d'expiration d'offre
export const sendOfferExpirationReminderEmail = async (email: string, data: {
  missionTitle: string;
  missionId: string;
  offerType: string;
  hoursRemaining: number;
  expirationDate: string;
  amount?: number;
  message?: string;
  senderName?: string;
}) => {
  try {
    const baseUrl = process.env.FRONTEND_URL || 'http://localhost:5173';

    // Valeurs par défaut pour les champs optionnels
    let senderName = data.senderName || 'Utilisateur';
    
    const amount = data.amount || 0;
    const message = data.message || 'Aucun message';

    const mailOptions = {
      from: `"Job Partiel" <${process.env.SMTP_FROM}>` || '<EMAIL>',
      to: email,
      subject: `${EMAIL_ICONS.offerExpirationReminder} Rappel : Offre en attente pour "${data.missionTitle}" - Expiration dans ${data.hoursRemaining}h`,
      html: `
        <div style="font-family: Arial, sans-serif; width: 100%; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #FFF8F3 0%, #FFE4BA 100%); padding: 40px 0;">
          <div style="background-color: white; padding: 40px; border-radius: 16px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
            <div style="text-align: center; margin-bottom: 30px;">
              <img src="${baseUrl}/logo.png" alt="JobPartiel Logo" style="max-width: 150px; height: auto;">
            </div>

            <div style="text-align: center; margin-bottom: 30px;">
              <div style="display: inline-block; background-color: #FFF8F3; border-radius: 50%; width: 80px; height: 80px; line-height: 80px; text-align: center; margin-bottom: 15px;">
                <span style="font-size: 40px;">⏰</span>
              </div>
              <h2 style="color: #FF7A35; font-size: 28px; margin: 10px 0;">Rappel important</h2>
              <p style="color: #4B5563; font-size: 18px; margin: 0;">Une offre attend votre réponse</p>
            </div>

            <div style="background-color: #FFF8F3; padding: 25px; border-radius: 12px; margin: 30px 0;">
              <p style="color: #374151; font-size: 16px; line-height: 1.8; margin-bottom: 20px;">
                Bonjour,
              </p>
              <p style="color: #374151; font-size: 16px; line-height: 1.8; margin-bottom: 20px;">
                Nous vous rappelons que vous avez ${data.offerType} en attente pour la mission <strong>"${data.missionTitle}"</strong>.
              </p>

              <div style="background-color: white; border-radius: 8px; padding: 20px; margin: 25px 0; border-left: 4px solid #FF7A35;">
                <p style="color: #4B5563; font-size: 14px; margin: 0 0 10px 0;">Détails de l'offre :</p>
                <p style="color: #374151; font-size: 15px; margin: 5px 0;"><strong>Proposée par :</strong> ${senderName}</p>
                <p style="color: #374151; font-size: 15px; margin: 5px 0;"><strong>Montant :</strong> ${amount.toFixed(2)}€</p>
                <p style="color: #374151; font-size: 15px; margin: 10px 0 5px 0;"><strong>Message :</strong></p>
                <div style="background-color: #F9FAFB; padding: 15px; border-radius: 6px; color: #4B5563; font-size: 14px; line-height: 1.6;">
                  ${message}
                </div>
              </div>

              <p style="color: #374151; font-size: 16px; line-height: 1.8; margin-bottom: 20px;">
                <strong>Cette offre expirera dans ${data.hoursRemaining} heures</strong> (le ${data.expirationDate}). Passé ce délai, elle sera automatiquement refusée.
              </p>
              <p style="color: #374151; font-size: 16px; line-height: 1.8; margin-bottom: 20px;">
                Veuillez y répondre rapidement pour éviter l'expiration automatique.
              </p>
            </div>

            <div style="text-align: center; margin: 35px 0;">
              <a href="${baseUrl}/dashboard/missions/offres?mission=${data.missionId}"
                 style="display: inline-block;
                        background-color: #FF7A35;
                        color: white;
                        padding: 16px 40px;
                        text-decoration: none;
                        border-radius: 12px;
                        font-weight: 600;
                        font-size: 18px;
                        transition: all 0.3s ease;
                        box-shadow: 0 4px 6px rgba(255, 122, 53, 0.2);">
                Répondre maintenant
              </a>
            </div>

            <div style="text-align: center; color: #6B7280; font-size: 14px; margin-top: 40px;">
              <p>Si vous avez des questions, n'hésitez pas à contacter notre équipe de support.</p>
              <p>© ${new Date().getFullYear()} Job Partiel. Tous droits réservés.</p>
            </div>
          </div>
        </div>
      `
    };

    await queueEmail(mailOptions.to, mailOptions.subject, mailOptions.html);
    logSecurity.info(LogEventType.EMAIL_SENT, 'Email de rappel d\'expiration d\'offre envoyé', { to: email });
  } catch (error) {
    if (error instanceof EmailServiceError) {
      throw error;
    }

    const errorMessage = error instanceof Error ? error.message : 'Une erreur inconnue est survenue';
    throw new EmailServiceError(
      EMAIL_ERRORS.SMTP_ERROR.message(errorMessage),
      EMAIL_ERRORS.SMTP_ERROR.code,
      { originalError: errorMessage }
    );
  }
};

// Email envoyé au filleul après son inscription avec un code de parrainage
export const sendReferralWelcomeEmail = async (email: string, data: {
  referrerName: string;
  rewardAmount: number;
}) => {
  try {
    // Vérification du rate limit
    checkRateLimit(email);

    const mailOptions = {
      from: `"Job Partiel" <${process.env.SMTP_FROM}>` || '<EMAIL>',
      to: email,
      subject: `${EMAIL_ICONS.referralWelcome} Bienvenue sur Job Partiel grâce à un parrainage !`,
      html: `
      <div style="font-family: 'Arial', sans-serif; width: 100%; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #FFF8F3 0%, #FFE4BA 100%); padding: 40px 0;">
        <div style="background-color: white; padding: 40px; border-radius: 16px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
          <div style="text-align: center; margin-bottom: 30px;">
            <img src="${config.apiUrl}/api/storage-proxy/photo_profil/avatar/avatar-defaut-jobpartiel.jpg" alt="Job Partiel" style="max-width: 100%; height: auto;">
          </div>

          <h2 style="color: #FF7A35; text-align: center; font-size: 28px; margin-bottom: 25px;">Bienvenue dans l'aventure Job Partiel !</h2>

          <p style="line-height: 1.8; color: #374151; font-size: 16px;">
            Bonjour,
          </p>

          <p style="font-size: 16px; margin-top: 15px;">
            Félicitations ! Vous venez de rejoindre Job Partiel grâce au parrainage de <strong>${data.referrerName}</strong>
          </p>

          <div style="
            margin: 25px auto;
            padding: 20px;
            background-color: #FFF8F3;
            border-radius: 12px;
            border-left: 4px solid #FF7A35;
          ">
            <h3 style="color: #FF7A35; margin-top: 0; font-size: 18px;">🎁 Votre récompense vous attend !</h3>
            <p style="margin-bottom: 0; font-size: 16px;">
              Complétez votre première mission sur la plateforme et recevez automatiquement <strong>${data.rewardAmount} Jobi</strong> en récompense de parrainage !
            </p>
          </div>

          <h3 style="color: #FF7A35; font-size: 20px; margin-top: 30px;">Comment obtenir votre récompense ?</h3>

          <ol style="padding-left: 20px; color: #374151; font-size: 16px; line-height: 1.8;">
            <li>Complétez votre profil pour augmenter vos chances d'être sélectionné</li>
            <li>Postulez à des missions qui correspondent à vos compétences</li>
            <li>Réalisez votre première mission avec succès</li>
            <li>Recevez automatiquement ${data.rewardAmount} Jobi sur votre compte</li>
          </ol>

          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.FRONTEND_URL}/dashboard/missions" style="
              display: inline-block;
              background-color: #FF7A35;
              color: white;
              padding: 14px 32px;
              text-decoration: none;
              border-radius: 8px;
              font-weight: 600;
              font-size: 16px;
              transition: all 0.3s ease;
              box-shadow: 0 2px 4px rgba(255, 122, 53, 0.2);
            ">Découvrir les missions</a>
          </div>

          <p style="font-size: 16px; color: #374151; margin-top: 20px;">
            Vous aussi, parrainez vos amis et gagnez des récompenses ! Rendez-vous dans votre espace personnel pour obtenir votre code de parrainage.
          </p>

          <div style="border-top: 1px solid #e0e0e0; margin-top: 30px; padding-top: 20px; text-align: center;">
            <p style="font-size: 14px; color: #666;">
              L'équipe Job Partiel vous souhaite une excellente expérience sur notre plateforme !
            </p>
          </div>
        </div>

        <div style="text-align: center; margin-top: 15px; color: #666; font-size: 12px;">
          ${new Date().getFullYear()} Job Partiel. Tous droits réservés.
        </div>
      </div>
      <style>
        @media only screen and (max-width: 600px) {
          h2 { font-size: 24px; }
          h3 { font-size: 18px; }
          ol { padding-left: 15px; }
          p, ol { font-size: 14px; }
        }
      </style>
    `
    };

    await queueEmail(mailOptions.to, mailOptions.subject, mailOptions.html);
    logSecurity.info(LogEventType.EMAIL_SENT, 'Email de bienvenue parrainage envoyé avec succès', { to: email });
    return true;
  } catch (error) {
    if (error instanceof EmailServiceError) {
      throw error;
    }

    const errorMessage = error instanceof Error ? error.message : 'Une erreur inconnue est survenue';
    throw new EmailServiceError(
      EMAIL_ERRORS.SMTP_ERROR.message(errorMessage),
      EMAIL_ERRORS.SMTP_ERROR.code,
      { originalError: errorMessage }
    );
  }
};

// Email envoyé au parrain quand quelqu'un s'inscrit avec son code
export const sendReferrerNotificationEmail = async (email: string, data: {
  referredName: string;
  rewardAmount: number;
  totalReferrals: number;
}) => {
  try {
    // Vérification du rate limit
    checkRateLimit(email);

    const mailOptions = {
      from: `"Job Partiel" <${process.env.SMTP_FROM}>` || '<EMAIL>',
      to: email,
      subject: `${EMAIL_ICONS.referrerNotification} Félicitations ! Quelqu\'un a utilisé votre code de parrainage`,
      html: `
      <div style="font-family: 'Arial', sans-serif; width: 100%; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #FFF8F3 0%, #FFE4BA 100%); padding: 40px 0;">
        <div style="background-color: white; padding: 40px; border-radius: 16px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
          <div style="text-align: center; margin-bottom: 30px;">
            <img src="${config.apiUrl}/api/storage-proxy/photo_profil/avatar/avatar-defaut-jobpartiel.jpg" alt="Job Partiel" style="max-width: 100%; height: auto;">
          </div>

          <h2 style="color: #FF7A35; text-align: center; font-size: 28px; margin-bottom: 25px;">Votre réseau s'agrandit !</h2>

          <p style="line-height: 1.8; color: #374151; font-size: 16px;">
            Bonjour,
          </p>

          <p style="font-size: 16px; margin-top: 15px;">
            Bonne nouvelle ! <strong>${data.referredName}</strong> vient de s'inscrire sur Job Partiel grâce à votre code de parrainage.
          </p>

          <div style="
            margin: 25px auto;
            padding: 20px;
            background-color: #FFF8F3;
            border-radius: 12px;
            border-left: 4px solid #FF7A35;
          ">
            <h3 style="color: #FF7A35; margin-top: 0; font-size: 18px;">💰 Récompense en attente</h3>
            <p style="margin-bottom: 0; font-size: 16px;">
              Dès que ${data.referredName} aura complété sa première mission, vous recevrez automatiquement <strong>${data.rewardAmount} Jobi</strong> !
            </p>
          </div>

          <div style="text-align: center; margin: 25px 0; padding: 15px; background-color: #f8f9fa; border-radius: 8px;">
            <p style="font-size: 16px; color: #374151; margin: 0 0 10px 0;">
              <strong>Statistiques de parrainage</strong>
            </p>
            <p style="font-size: 18px; color: #FF7A35; font-weight: bold; margin: 0;">
              ${data.totalReferrals} ${data.totalReferrals > 1 ? 'personnes parrainées' : 'personne parrainée'}
            </p>
          </div>

          <h3 style="color: #FF7A35; font-size: 20px; margin-top: 30px;">Comment augmenter vos chances de récompense ?</h3>

          <ul style="padding-left: 20px; color: #374151; font-size: 16px; line-height: 1.8;">
            <li>Encouragez ${data.referredName} à compléter son profil</li>
            <li>Partagez vos conseils pour trouver des missions intéressantes</li>
            <li>Continuez à parrainer d'autres personnes pour maximiser vos gains</li>
          </ul>

          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.FRONTEND_URL}/dashboard/parametres" style="
              display: inline-block;
              background-color: #FF7A35;
              color: white;
              padding: 14px 32px;
              text-decoration: none;
              border-radius: 8px;
              font-weight: 600;
              font-size: 16px;
              transition: all 0.3s ease;
              box-shadow: 0 2px 4px rgba(255, 122, 53, 0.2);
            ">Gérer mes parrainages</a>
          </div>

          <div style="border-top: 1px solid #e0e0e0; margin-top: 30px; padding-top: 20px; text-align: center;">
            <p style="font-size: 14px; color: #666;">
              Merci de contribuer à la croissance de la communauté Job Partiel !
            </p>
          </div>
        </div>

        <div style="text-align: center; margin-top: 15px; color: #666; font-size: 12px;">
          ${new Date().getFullYear()} Job Partiel. Tous droits réservés.
        </div>
      </div>
      <style>
        @media only screen and (max-width: 600px) {
          h2 { font-size: 24px; }
          h3 { font-size: 18px; }
          ul { padding-left: 15px; }
          p, ul { font-size: 14px; }
        }
      </style>
    `
    };

    await queueEmail(mailOptions.to, mailOptions.subject, mailOptions.html);
    logSecurity.info(LogEventType.EMAIL_SENT, 'Email de notification parrain envoyé avec succès', { to: email });
    return true;
  } catch (error) {
    if (error instanceof EmailServiceError) {
      throw error;
    }

    const errorMessage = error instanceof Error ? error.message : 'Une erreur inconnue est survenue';
    throw new EmailServiceError(
      EMAIL_ERRORS.SMTP_ERROR.message(errorMessage),
      EMAIL_ERRORS.SMTP_ERROR.code,
      { originalError: errorMessage }
    );
  }
};

// Email envoyé quand la récompense de parrainage est attribuée
export const sendReferralRewardEmail = async (emails: { referrer: string, referred: string }, data: {
  referrerName: string,
  referredName: string,
  fullReferrerName?: string,
  fullReferredName?: string,
  rewardAmount: number,
  missionTitle: string
}) => {
  try {
    // Emails à envoyer
    const recipients = [
      {
        email: emails.referrer,
        isReferrer: true
      },
      {
        email: emails.referred,
        isReferrer: false
      }
    ];

    for (const recipient of recipients) {
      // Vérification du rate limit
      checkRateLimit(recipient.email);

      // Déterminer les noms à afficher
      const displayReferrerName = recipient.isReferrer ? "vous" : (data.referrerName || "Un utilisateur");
      const displayReferredName = !recipient.isReferrer ? "vous" : (data.referredName || "Un utilisateur");

      // Noms complets pour les détails
      const fullReferrerName = data.fullReferrerName || data.referrerName;
      const fullReferredName = data.fullReferredName || data.referredName;

      const mailOptions = {
        from: `"Job Partiel" <${process.env.SMTP_FROM}>` || '<EMAIL>',
        to: recipient.email,
        subject: `${EMAIL_ICONS.referralReward} Récompense de parrainage crédée !`,
        html: `
        <div style="font-family: 'Arial', sans-serif; width: 100%; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #FFF8F3 0%, #FFE4BA 100%); padding: 40px 0;">
          <div style="background-color: white; padding: 40px; border-radius: 16px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
            <div style="text-align: center; margin-bottom: 30px;">
              <img src="${config.apiUrl}/api/storage-proxy/photo_profil/avatar/avatar-defaut-jobpartiel.jpg" alt="Job Partiel" style="max-width: 100%; height: auto;">
            </div>

            <h2 style="color: #FF7A35; text-align: center; font-size: 28px; margin-bottom: 25px;">
              ${recipient.isReferrer ? 'Votre parrainage a porté ses fruits !' : 'Félicitations pour votre première mission !'}
            </h2>

            <p style="line-height: 1.8; color: #374151; font-size: 16px;">
              Bonjour,
            </p>

            <p style="font-size: 16px; margin-top: 15px;">
              ${recipient.isReferrer
                ? `Excellente nouvelle ! <strong>${data.referredName}</strong> vient de terminer sa première mission sur Job Partiel.`
                : `Félicitations pour avoir terminé votre première mission "<strong>${data.missionTitle}</strong>" sur Job Partiel !`}
            </p>

            <div style="
              margin: 25px auto;
              padding: 20px;
              background-color: #FFF8F3;
              border-radius: 12px;
              border-left: 4px solid #FF7A35;
              text-align: center;
            ">
              <h3 style="color: #FF7A35; margin-top: 0; font-size: 22px;">🎉 Récompense créditée !</h3>
              <p style="font-size: 18px; font-weight: bold; color: #374151;">
                <span style="font-size: 24px; color: #FF7A35;">${data.rewardAmount}</span> Jobi
              </p>
              <p style="margin-bottom: 0; font-size: 16px;">
                ${recipient.isReferrer
                  ? 'ont été ajoutés à votre compte en récompense de votre parrainage.'
                  : 'ont été ajoutés à votre compte en récompense de votre inscription par parrainage.'}
              </p>
            </div>

            <div style="margin: 25px auto; padding: 20px; background-color: #f8f9fa; border-radius: 12px;">
              <h3 style="color: #374151; margin-top: 0; font-size: 18px; text-align: center;">Détails du parrainage</h3>
              <table style="width: 100%; border-collapse: collapse; margin-top: 15px;">
                <tr>
                  <td style="padding: 8px; border-bottom: 1px solid #e5e7eb; color: #6b7280; font-size: 14px;">Parrain</td>
                  <td style="padding: 8px; border-bottom: 1px solid #e5e7eb; font-size: 14px; text-align: right;"><strong>${fullReferrerName}</strong></td>
                </tr>
                <tr>
                  <td style="padding: 8px; border-bottom: 1px solid #e5e7eb; color: #6b7280; font-size: 14px;">Filleul</td>
                  <td style="padding: 8px; border-bottom: 1px solid #e5e7eb; font-size: 14px; text-align: right;"><strong>${fullReferredName}</strong></td>
                </tr>
                <tr>
                  <td style="padding: 8px; border-bottom: 1px solid #e5e7eb; color: #6b7280; font-size: 14px;">Récompense</td>
                  <td style="padding: 8px; border-bottom: 1px solid #e5e7eb; font-size: 14px; text-align: right;"><strong>${data.rewardAmount} Jobi</strong></td>
            ${recipient.isReferrer ? `
            <p style="font-size: 16px; color: #374151; margin-top: 20px;">
              Continuez à parrainer vos amis et connaissances pour gagner encore plus de Jobi !
            </p>

            <div style="text-align: center; margin: 30px 0;">
              <a href="${process.env.FRONTEND_URL}/dashboard/parametres" style="
                display: inline-block;
                background-color: #FF7A35;
                color: white;
                padding: 14px 32px;
                text-decoration: none;
                border-radius: 8px;
                font-weight: 600;
                font-size: 16px;
                transition: all 0.3s ease;
                box-shadow: 0 2px 4px rgba(255, 122, 53, 0.2);
              ">Gérer mes parrainages</a>
            </div>
            ` : `
            <p style="font-size: 16px; color: #374151; margin-top: 20px;">
              Vous pouvez maintenant utiliser vos Jobi pour échanger des services sur la plateforme.
            </p>

            <div style="text-align: center; margin: 30px 0;">
              <a href="${process.env.FRONTEND_URL}/dashboard/wallet" style="
                display: inline-block;
                background-color: #FF7A35;
                color: white;
                padding: 14px 32px;
                text-decoration: none;
                border-radius: 8px;
                font-weight: 600;
                font-size: 16px;
                transition: all 0.3s ease;
                box-shadow: 0 2px 4px rgba(255, 122, 53, 0.2);
              ">Voir mon portefeuille</a>
            </div>
            `}

            <div style="border-top: 1px solid #e0e0e0; margin-top: 30px; padding-top: 20px; text-align: center;">
              <p style="font-size: 14px; color: #666;">
                Merci de faire partie de la communauté Job Partiel !
              </p>
            </div>
          </div>

          <div style="text-align: center; margin-top: 15px; color: #666; font-size: 12px;">
            ${new Date().getFullYear()} Job Partiel. Tous droits réservés.
          </div>
        </div>
        <style>
          @media only screen and (max-width: 600px) {
            h2 { font-size: 24px; }
            h3 { font-size: 18px; }
            p { font-size: 14px; }
          }
        </style>
      `
      };

      await queueEmail(mailOptions.to, mailOptions.subject, mailOptions.html);
      logSecurity.info(LogEventType.EMAIL_SENT, 'Email de récompense parrainage envoyé avec succès', {
        to: recipient.email,
        isReferrer: recipient.isReferrer
      });
    }

    return true;
  } catch (error) {
    if (error instanceof EmailServiceError) {
      throw error;
    }

    const errorMessage = error instanceof Error ? error.message : 'Une erreur inconnue est survenue';
    throw new EmailServiceError(
      EMAIL_ERRORS.SMTP_ERROR.message(errorMessage),
      EMAIL_ERRORS.SMTP_ERROR.code,
      { originalError: errorMessage }
    );
  }
};

// Envoyer un email pour un nouveau rapport de bug
export const sendNewBugReportEmail = async (email: string, data: {
  reportTitle: string;
  reportDescription: string;
  reportType: string;
  category: string;
  priority: string;
  reportId: string;
  reporterEmail?: string;
  isPrivate: boolean;
}) => {
  try {
    if (!isValidEmail(email)) {
      throw new EmailServiceError(
        EMAIL_ERRORS.INVALID_EMAIL.message(email),
        EMAIL_ERRORS.INVALID_EMAIL.code,
        { email }
      );
    }

    // Vérifier la limite de débit
    if (!checkRateLimit(email)) {
      throw new EmailServiceError(
        "La limite de débit a été atteinte pour cet email",
        EMAIL_ERRORS.RATE_LIMIT.code,
        { email }
      );
    }

    logSecurity.info(LogEventType.EMAIL_SENT, 'Envoi d\'un email de notification de nouveau rapport de bug', { email });

    // Formatage des données pour l'email
    const typeReport = data.reportType === 'bug' ? 'bug' : 'amélioration';
    const priorityText = {
      'faible': 'Faible',
      'moyenne': 'Moyenne',
      'elevee': 'Élevée',
      'critique': 'Critique'
    }[data.priority] || data.priority;

    const categoryText = {
      'interface': 'Interface',
      'fonctionnalite': 'Fonctionnalité',
      'paiement': 'Paiement',
      'securite': 'Sécurité',
      'autre': 'Autre'
    }[data.category] || data.category;

    // Construction du contenu de l'email
    const emailContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background-color: #FFF8F3; padding: 20px; border-radius: 8px; border-top: 5px solid #FF6B2C;">
        <div style="text-align: center; margin-bottom: 20px;">
          <h1 style="color: #FF6B2C; margin-bottom: 10px;">Nouveau rapport ${typeReport.match(/^[aeiouhAEIOUH]/i) ? "d'" : "de "}${typeReport}</h1>
          ${data.isPrivate ? `<p style="display: inline-block; background-color: #FF6B2C; color: white; padding: 6px 12px; border-radius: 20px; font-size: 14px; margin-top: 0;">Rapport Privé</p>` : ''}
        </div>

        <div style="background-color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; border: 1px solid #FFE4BA;">
          <h2 style="color: #FF7A35; font-size: 18px; margin-top: 0;">Informations du rapport</h2>

          <p><strong>Titre:</strong> ${data.reportTitle}</p>
          <p><strong>Description:</strong> ${data.reportDescription.substring(0, 200)}${data.reportDescription.length > 200 ? '...' : ''}</p>
          <p><strong>Type:</strong> ${typeReport}</p>
          <p><strong>Catégorie:</strong> ${categoryText}</p>
          <p><strong>Priorité:</strong> ${priorityText}</p>
          ${data.reporterEmail ? `<p><strong>Envoyé par:</strong> ${data.reporterEmail}</p>` : ''}
          <p><strong>Confidentialité:</strong> <span style="${data.isPrivate ? 'color: #FF6B2C; font-weight: bold;' : ''}">${data.isPrivate ? 'Privé (visible uniquement par l\'auteur et l\'équipe d\'administration)' : 'Public (après modération)'}</span></p>
        </div>

        <div style="text-align: center;">
          <a href="${process.env.FRONTEND_URL}/admin/bug-reports/${data.reportId}" style="display: inline-block; background-color: #FF6B2C; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;">
            Voir le rapport complet
          </a>
        </div>

        <div style="margin-top: 30px; font-size: 12px; color: #666; text-align: center;">
          <p>Cet email a été envoyé automatiquement par JobPartiel.</p>
          <p>© ${new Date().getFullYear()} JobPartiel. Tous droits réservés.</p>
        </div>
      </div>
    `;

    const mailOptions = {
      from: `"Job Partiel" <${process.env.SMTP_FROM}>` || '<EMAIL>',
      to: email,
      subject: `${EMAIL_ICONS.newBugReport} Nouveau rapport ${typeReport.match(/^[aeiouhAEIOUH]/i) ? "d'" : "de "}${typeReport}: ${data.reportTitle}${data.isPrivate ? ' [PRIVÉ]' : ''}`,
      html: emailContent
    };

    await queueEmail(mailOptions.to, mailOptions.subject, mailOptions.html);

    logSecurity.info(LogEventType.EMAIL_SENT, 'Email de notification de nouveau rapport de bug envoyé avec succès', { email });
    return true;
  } catch (error) {
    logSecurity.error(LogEventType.EMAIL_ERROR, 'Erreur lors de l\'envoi de l\'email de notification de nouveau rapport de bug', { error, email });
    throw error;
  }
};

// Envoyer un email pour un nouveau commentaire sur un rapport de bug
export const sendBugReportCommentEmail = async (email: string, data: {
  reportTitle: string;
  commentMessage: string;
  reportId: string;
  commenterId: string;
  commenterEmail: string;
  isAdmin: boolean;
}) => {
  try {
    if (!isValidEmail(email)) {
      throw new EmailServiceError(
        EMAIL_ERRORS.INVALID_EMAIL.message(email),
        EMAIL_ERRORS.INVALID_EMAIL.code,
        { email }
      );
    }

    // Vérifier la limite de débit
    if (!checkRateLimit(email)) {
      throw new EmailServiceError(
        "La limite de débit a été atteinte pour cet email",
        EMAIL_ERRORS.RATE_LIMIT.code,
        { email }
      );
    }

    logSecurity.info(LogEventType.EMAIL_SENT, 'Envoi d\'un email de notification de nouveau commentaire sur un rapport de bug', { email });

    // Construction du contenu de l'email
    const emailContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background-color: #FFF8F3; padding: 20px; border-radius: 8px; border-top: 5px solid #FF6B2C;">
        <div style="text-align: center; margin-bottom: 20px;">
          <h1 style="color: #FF6B2C; margin-bottom: 10px;">Nouveau commentaire sur votre rapport</h1>
        </div>

        <div style="background-color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; border: 1px solid #FFE4BA;">
          <h2 style="color: #FF7A35; font-size: 18px; margin-top: 0;">Détails du commentaire</h2>

          <p><strong>Rapport:</strong> ${data.reportTitle}</p>
          <p><strong>Commentaire:</strong> ${data.commentMessage}</p>
          <p><strong>De:</strong> ${data.isAdmin ? 'Support JobPartiel' : data.commenterEmail}</p>
        </div>

        <div style="text-align: center;">
          <a href="${process.env.FRONTEND_URL}/${data.isAdmin ? 'dashboard' : 'admin'}/bug-reports/${data.reportId}" style="display: inline-block; background-color: #FF6B2C; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;">
            Voir le rapport complet
          </a>
        </div>

        <div style="margin-top: 30px; font-size: 12px; color: #666; text-align: center;">
          <p>Cet email a été envoyé automatiquement par JobPartiel.</p>
          <p>© ${new Date().getFullYear()} JobPartiel. Tous droits réservés.</p>
        </div>
      </div>
    `;

    const mailOptions = {
      from: `"Job Partiel" <${process.env.SMTP_FROM}>` || '<EMAIL>',
      to: email,
      subject: `${EMAIL_ICONS.bugReportComment} Nouveau commentaire sur le rapport: ${data.reportTitle}`,
      html: emailContent
    };

    await queueEmail(mailOptions.to, mailOptions.subject, mailOptions.html);

    logSecurity.info(LogEventType.EMAIL_SENT, 'Email de notification de nouveau commentaire sur un rapport de bug envoyé avec succès', { email });
    return true;
  } catch (error) {
    logSecurity.error(LogEventType.EMAIL_ERROR, 'Erreur lors de l\'envoi de l\'email de notification de nouveau commentaire', { error, email });
    throw error;
  }
};

export const sendBugReportStatusChangeEmail = async (email: string, data: {
  reportTitle: string;
  oldStatus: string;
  newStatus: string;
  adminComment: string;
  reportId: string;
}) => {
  try {
    if (!isValidEmail(email)) {
      throw new EmailServiceError(
        EMAIL_ERRORS.INVALID_EMAIL.message(email),
        EMAIL_ERRORS.INVALID_EMAIL.code,
        { email }
      );
    }

    if (!checkRateLimit(email)) {
      throw new EmailServiceError(
        EMAIL_ERRORS.RATE_LIMIT.message(email, rateLimitConfig.maxEmails, rateLimitConfig.windowMs),
        EMAIL_ERRORS.RATE_LIMIT.code,
        { email, maxEmails: rateLimitConfig.maxEmails, windowMs: rateLimitConfig.windowMs }
      );
    }

    // Formater les statuts pour affichage
    const formatStatus = (status: string): string => {
      switch (status) {
        case 'nouveau': return 'Nouveau';
        case 'en_cours': return 'En cours';
        case 'resolu': return 'Résolu';
        case 'rejete': return 'Rejeté';
        case 'ferme': return 'Fermé';
        case 'reouvert': return 'Réouvert';
        default: return status;
      }
    };

    const oldStatusFormatted = formatStatus(data.oldStatus);
    const newStatusFormatted = formatStatus(data.newStatus);

    // Créer un titre approprié pour l'email
    let titre = '';
    switch (data.newStatus) {
      case 'ferme':
        titre = `Votre rapport "${data.reportTitle}" a été fermé`;
        break;
      case 'reouvert':
        titre = `Votre rapport "${data.reportTitle}" a été réouvert`;
        break;
      default:
        titre = `Statut de votre rapport "${data.reportTitle}" mis à jour`;
    }

    // Couleur selon le statut - avec normalisation pour garantir la correspondance
    let statusColor = '#FF7A35'; // Couleur par défaut (orange JobPartiel)
    const normalizedStatus = String(data.newStatus).trim().toLowerCase();

    // Attribution des couleurs selon le statut normalisé
    // Nous utilisons plusieurs formats possibles pour augmenter les chances de correspondance
    if (normalizedStatus === 'resolu' || normalizedStatus === 'résolu') {
      statusColor = '#4CAF50'; // Vert
      logSecurity.info(LogEventType.EMAIL_SENT, 'Couleur verte sélectionnée (résolu)', { statusColor });
    } else if (normalizedStatus === 'rejete' || normalizedStatus === 'rejeté' || normalizedStatus === 'ferme' || normalizedStatus === 'fermé') {
      statusColor = '#F44336'; // Rouge
      logSecurity.info(LogEventType.EMAIL_SENT, 'Couleur rouge sélectionnée (rejeté/fermé)', { statusColor });
    } else if (normalizedStatus === 'en_cours') {
      statusColor = '#2196F3'; // Bleu
      logSecurity.info(LogEventType.EMAIL_SENT, 'Couleur bleue sélectionnée (en cours)', { statusColor });
    } else if (normalizedStatus === 'reouvert' || normalizedStatus === 'réouvert') {
      statusColor = '#FF9800'; // Orange
      logSecurity.info(LogEventType.EMAIL_SENT, 'Couleur orange sélectionnée (réouvert)', { statusColor });
    } else {
      logSecurity.info(LogEventType.EMAIL_SENT, 'Couleur par défaut sélectionnée', { statusColor, normalizedStatus });
    }

    // Construction du message HTML avec une méthode améliorée pour afficher le statut
    const htmlContent = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${titre}</title>
    </head>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f5f5f5;">
      <div style="max-width: 600px; margin: 0 auto; background-color: #FFF8F3; border-radius: 8px; overflow: hidden; border-top: 5px solid #FF6B2C; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); margin-top: 20px;">

        <!-- En-tête -->
        <div style="background-color: #FF6B2C; padding: 20px; text-align: center;">
          <h1 style="color: white; margin: 0; font-size: 24px;">${titre}</h1>
        </div>

        <!-- Contenu principal -->
        <div style="padding: 30px;">
          <p style="margin-top: 0;">Bonjour,</p>
          <p>Nous vous informons que le statut de votre rapport de bug a été mis à jour.</p>

          <!-- Informations du rapport -->
          <div style="background-color: white; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #FFE4BA;">
            <h2 style="color: #FF7A35; font-size: 18px; margin-top: 0;">Informations du rapport</h2>
            <p style="margin-bottom: 5px;"><strong>Titre :</strong> ${data.reportTitle}</p>

            <!-- Changement de statut - Version améliorée pour garantir la compatibilité email -->
            <div style="margin: 15px 0;">
              <p style="margin-bottom: 5px;"><strong>Changement de statut :</strong></p>
              <table cellpadding="0" cellspacing="0" border="0" width="100%" style="margin-bottom: 10px;">
                <tr>
                  <td width="50%" style="padding-right: 5px;">
                    <div style="background-color: #f0f0f0; padding: 10px; border-radius: 4px; text-align: center;">
                      <p style="margin: 0; font-size: 12px; color: #666; text-transform: uppercase;">Ancien</p>
                      <p style="margin: 5px 0 0; font-weight: bold; color: #555;">${oldStatusFormatted}</p>
                    </div>
                  </td>
                  <td width="50%" style="padding-left: 5px;">
                    <div style="background-color: ${statusColor}; padding: 10px; border-radius: 4px; text-align: center;">
                      <p style="margin: 0; font-size: 12px; color: white; text-transform: uppercase;">Nouveau</p>
                      <p style="margin: 5px 0 0; font-weight: bold; color: white;">${newStatusFormatted}</p>
                    </div>
                  </td>
                </tr>
              </table>
            </div>
          </div>

          <!-- Commentaire admin si présent -->
          ${data.adminComment ? `
          <div style="background-color: white; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #FFE4BA;">
            <h3 style="color: #FF7A35; font-size: 16px; margin-top: 0;">Message de l'équipe JobPartiel</h3>
            <p style="margin-bottom: 0;">${data.adminComment}</p>
          </div>
          ` : ''}

          <!-- Bouton d'action -->
          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.FRONTEND_URL || 'https://jobpartiel.fr'}/dashboard/bug-reports/${data.reportId}"
               style="display: inline-block; background-color: #FF6B2C; color: white; padding: 12px 25px; text-decoration: none; border-radius: 4px; font-weight: bold;">
              Voir le rapport
            </a>
          </div>

          <p>Si vous avez des questions, n'hésitez pas à nous contacter.</p>

          <p style="margin-bottom: 0;">Cordialement,<br>L'équipe JobPartiel</p>
        </div>

        <!-- Pied de page -->
        <div style="background-color: #f9f9f9; padding: 15px; text-align: center; font-size: 12px; color: #777; border-top: 1px solid #eee;">
          <p style="margin-top: 0;">Cet email a été envoyé automatiquement, merci de ne pas y répondre directement.</p>
          <p style="margin-bottom: 0;">© ${new Date().getFullYear()} JobPartiel - Tous droits réservés</p>
        </div>
      </div>
    </body>
    </html>
    `;

    const mailOptions = {
      from: `"Job Partiel" <${process.env.SMTP_FROM}>` || '<EMAIL>',
      to: email,
      subject: titre,
      html: htmlContent
    };

    await queueEmail(mailOptions.to, mailOptions.subject, mailOptions.html);
    logSecurity.info(LogEventType.EMAIL_SENT, 'Email de notification de changement de statut de bug report envoyé', { email });
  } catch (error) {
    logSecurity.error(LogEventType.EMAIL_ERROR, 'Erreur lors de l\'envoi de l\'email de notification de changement de statut de bug report', { email, error });
    throw new EmailServiceError(
      EMAIL_ERRORS.SENDING_ERROR.message('Erreur inconnue'),
      EMAIL_ERRORS.SENDING_ERROR.code,
      { email, error }
    );
  }
};

export const sendEmailChangeNotification = async (email: string, data: {
  oldEmail: string;
  newEmail: string;
}) => {
  try {
    if (!isValidEmail(email)) {
      throw new EmailServiceError(
        EMAIL_ERRORS.INVALID_EMAIL.message(email),
        EMAIL_ERRORS.INVALID_EMAIL.code,
        { email }
      );
    }

    const mailOptions = {
      from: `"Job Partiel" <${process.env.SMTP_FROM}>` || '<EMAIL>',
      to: email,
      subject: 'Votre adresse email a été modifiée',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
          <div style="text-align: center; margin-bottom: 20px;">
            <img src="https://jobpartiel.fr/assets/logos/logo.svg" alt="JobPartiel Logo" style="max-width: 200px;">
          </div>
          <h2 style="color: #FF6B2C; text-align: center;">Changement d'adresse email</h2>
          <p>Bonjour,</p>
          <p>Nous vous informons que l'adresse email associée à votre compte JobPartiel a été modifiée.</p>
          <p>Votre adresse email <strong>${data.oldEmail}</strong> a été remplacée par <strong>${data.newEmail}</strong>.</p>
          <p>Si vous n'êtes pas à l'origine de ce changement, veuillez contacter immédiatement notre support à l'adresse <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
          <div style="background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <p style="margin: 0; color: #555;">Pour des raisons de sécurité, cette notification a été envoyée à votre ancienne adresse email.</p>
          </div>
          <p>Cordialement,</p>
          <p>L'équipe JobPartiel</p>
          <div style="margin-top: 20px; padding-top: 20px; border-top: 1px solid #e0e0e0; font-size: 12px; color: #999; text-align: center;">
            <p>Si vous n'êtes pas à l'origine de cette demande, veuillez ignorer cet email.</p>
            <p>&copy; ${new Date().getFullYear()} JobPartiel. Tous droits réservés.</p>
          </div>
        </div>
      `
    };

    await queueEmail(mailOptions.to, mailOptions.subject, mailOptions.html);

    logSecurity.info(LogEventType.EMAIL_SENT, 'Email de notification de changement d\'adresse envoyé avec succès', { email });
    return true;
  } catch (error) {
    logSecurity.error(LogEventType.EMAIL_ERROR, 'Erreur lors de l\'envoi de l\'email de notification de changement d\'adresse', { error, email });
    throw error;
  }
};

export const sendEmailChangeConfirmation = async (email: string) => {
  try {
    if (!isValidEmail(email)) {
      throw new EmailServiceError(
        EMAIL_ERRORS.INVALID_EMAIL.message(email),
        EMAIL_ERRORS.INVALID_EMAIL.code,
        { email }
      );
    }

    const mailOptions = {
      from: `"Job Partiel" <${process.env.SMTP_FROM}>` || '<EMAIL>',
      to: email,
      subject: 'Votre adresse email a été mise à jour avec succès',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
          <div style="text-align: center; margin-bottom: 20px;">
            <img src="https://jobpartiel.fr/assets/logos/logo.svg" alt="JobPartiel Logo" style="max-width: 200px;">
          </div>
          <h2 style="color: #FF6B2C; text-align: center;">Adresse email mise à jour</h2>
          <p>Bonjour,</p>
          <p>Félicitations ! Votre adresse email a été mise à jour avec succès sur JobPartiel.</p>
          <p>Vous recevez désormais les notifications et communications à cette adresse email.</p>
          <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <p style="margin: 0; color: #333;">Si vous avez des questions ou besoin d'aide, n'hésitez pas à contacter notre support à <a href="http://localhost:5173/dashboard/support/new">Contacter le support</a>.</p>
          </div>
          <p>Cordialement,</p>
          <p>L'équipe JobPartiel</p>
          <div style="margin-top: 20px; padding-top: 20px; border-top: 1px solid #e0e0e0; font-size: 12px; color: #999; text-align: center;">
            <p>&copy; ${new Date().getFullYear()} JobPartiel. Tous droits réservés.</p>
          </div>
        </div>
      `
    };

    await queueEmail(mailOptions.to, mailOptions.subject, mailOptions.html);

    logSecurity.info(LogEventType.EMAIL_SENT, 'Email de confirmation de changement d\'adresse envoyé avec succès', { email });
    return true;
  } catch (error) {
    logSecurity.error(LogEventType.EMAIL_ERROR, 'Erreur lors de l\'envoi de l\'email de confirmation de changement d\'adresse', { error, email });
    throw error;
  }
};

export const sendNewSupportTicketEmail = async (email: string, data: {
  ticketId: string;
  title: string;
  description: string;
  priority: string;
  category: string;
  userEmail: string;
}) => {
  try {
    const baseUrl = process.env.FRONTEND_URL || 'http://localhost:5173';

    const mailOptions = {
      from: `"Job Partiel" <${process.env.SMTP_FROM}>` || '<EMAIL>',
      to: email,
      subject: `${EMAIL_ICONS.newSupportTicket} ${EMAIL_SUBJECTS.newSupportTicket} #${data.ticketId}`,
      html: `
        <div style="font-family: Arial, sans-serif; width: 100%; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #FFF8F3 0%, #FFE4BA 100%); padding: 40px 0;">
          <div style="background-color: white; padding: 40px; border-radius: 16px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
            <div style="text-align: center; margin-bottom: 30px;">
              <img src="${baseUrl}/logo.png" alt="JobPartiel Logo" style="max-width: 150px; height: auto;">
            </div>

            <div style="text-align: center; margin-bottom: 30px;">
              <div style="display: inline-block; background-color: #FFF8F3; border-radius: 50%; width: 80px; height: 80px; line-height: 80px; text-align: center; margin-bottom: 15px;">
                <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="#FF7A35">
                  <path d="M20 12v6a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2v-6M4 12V6a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v6M2 10h20M7 15h.01M11 15h.01M15 15h.01"></path>
                </svg>
              </div>
              <h2 style="color: #FF7A35; font-size: 28px; margin: 10px 0;">Nouveau ticket de support</h2>
              <p style="color: #4B5563; font-size: 18px; margin: 0;">Un nouveau ticket a été créé</p>
            </div>

            <div style="background-color: #FFF8F3; padding: 25px; border-radius: 12px; margin: 30px 0;">
              <h3 style="color: #FF7A35; margin: 0 0 15px 0;">Détails du ticket :</h3>
              <p style="color: #374151; margin: 5px 0;"><strong>Titre :</strong> ${data.title}</p>
              <p style="color: #374151; margin: 5px 0;"><strong>Catégorie :</strong> ${data.category}</p>
              <p style="color: #374151; margin: 5px 0;"><strong>Priorité :</strong> ${data.priority}</p>
              <p style="color: #374151; margin: 15px 0 5px 0;"><strong>Description :</strong></p>
              <div style="background-color: white; padding: 15px; border-radius: 8px; margin-top: 10px;">
                <p style="color: #4B5563; margin: 0;">${data.description}</p>
              </div>
            </div>

            <div style="text-align: center; margin-top: 30px;">
              <a href="${baseUrl}/support/tickets/${data.ticketId}"
                 style="display: inline-block; background-color: #FF7A35; color: white; text-decoration: none; padding: 12px 25px; border-radius: 8px; font-weight: bold;">
                Voir le ticket
              </a>
            </div>

            <div style="background-color: #FFF8F3; padding: 15px; border-radius: 8px; margin-top: 30px; border-left: 4px solid #FF965E;">
              <p style="color: #4B5563; margin: 0; font-size: 14px;">
                <strong>Note importante :</strong> Ce ticket sera automatiquement fermé après 30 jours sans activité.
                Pour éviter cela, assurez-vous de répondre aux commentaires ou de mettre à jour le ticket régulièrement.
              </p>
            </div>
          </div>

          <div style="text-align: center; margin-top: 20px; color: #6B7280; font-size: 14px;">
            <p style="margin: 5px 0;">© ${new Date().getFullYear()} JobPartiel - Tous droits réservés</p>
          </div>
        </div>
      `
    };

    await queueEmail(mailOptions.to, mailOptions.subject, mailOptions.html);
    logSecurity.info(LogEventType.EMAIL_SENT, 'Email de nouveau ticket de support envoyé', { email });
  } catch (error) {
    logSecurity.error(LogEventType.EMAIL_ERROR, 'Erreur lors de l\'envoi de l\'email de nouveau ticket de support', { email, error });
    throw error;
  }
};

export const sendSupportTicketCommentEmail = async (email: string, data: {
  ticketId: string;
  title: string;
  commentMessage: string;
  commenterEmail: string;
  isInternal: boolean;
  ticketUrl: string;
}) => {
  try {
    const baseUrl = process.env.FRONTEND_URL || 'http://localhost:5173';

    const mailOptions = {
      from: `"Job Partiel" <${process.env.SMTP_FROM}>` || '<EMAIL>',
      to: email,
      subject: `${EMAIL_ICONS.supportTicketComment} ${EMAIL_SUBJECTS.supportTicketComment} : ${data.title}`,
      html: `
        <div style="font-family: Arial, sans-serif; width: 100%; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #FFF8F3 0%, #FFE4BA 100%); padding: 40px 0;">
          <div style="background-color: white; padding: 40px; border-radius: 16px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
            <div style="text-align: center; margin-bottom: 30px;">
              <img src="${baseUrl}/logo.png" alt="JobPartiel Logo" style="max-width: 150px; height: auto;">
            </div>

            <div style="text-align: center; margin-bottom: 30px;">
              <div style="display: inline-block; background-color: #FFF8F3; border-radius: 50%; width: 80px; height: 80px; line-height: 80px; text-align: center; margin-bottom: 15px;">
                <span style="font-size: 40px;">💬</span>
              </div>
              <h2 style="color: #FF7A35; font-size: 28px; margin: 10px 0;">Nouveau commentaire</h2>
              <p style="color: #4B5563; font-size: 18px; margin: 0;">Sur le ticket : ${data.title}</p>
            </div>

            <div style="background-color: #FFF8F3; padding: 25px; border-radius: 12px; margin: 30px 0;">
              <p style="color: #374151; margin: 0 0 15px 0;">
                <strong>De :</strong> ${data.commenterEmail}
              </p>
              <div style="background-color: white; padding: 20px; border-radius: 8px;">
                <p style="color: #4B5563; margin: 0;">${data.commentMessage}</p>
              </div>
              ${data.isInternal ? '<p style="color: #FF7A35; margin: 15px 0 0 0; font-style: italic;">Note interne</p>' : ''}
            </div>

            <div style="text-align: center; margin-top: 30px;">
              <a href="${data.ticketUrl}"
                 style="display: inline-block; background-color: #FF7A35; color: white; text-decoration: none; padding: 12px 25px; border-radius: 8px; font-weight: bold;">
                Voir le ticket
              </a>
            </div>
          </div>

          <div style="text-align: center; margin-top: 20px; color: #6B7280; font-size: 14px;">
            <p style="margin: 5px 0;">© ${new Date().getFullYear()} JobPartiel - Tous droits réservés</p>
          </div>
        </div>
      `
    };

    await queueEmail(mailOptions.to, mailOptions.subject, mailOptions.html);
    logSecurity.info(LogEventType.EMAIL_SENT, 'Email de nouveau commentaire sur ticket de support envoyé', { email });
  } catch (error) {
    logSecurity.error(LogEventType.EMAIL_ERROR, 'Erreur lors de l\'envoi de l\'email de nouveau commentaire sur ticket de support', { email, error });
    throw error;
  }
};

export const sendSupportTicketStatusChangeEmail = async (email: string, data: {
  ticketId: string;
  title: string;
  oldStatus: string;
  newStatus: string;
  userEmail: string;
  isAutomatic?: boolean;
  isReopenedByComment?: boolean;
}) => {
  try {
    const baseUrl = process.env.FRONTEND_URL || 'http://localhost:5173';

    // Déterminer si l'email est envoyé à l'utilisateur ou au staff
    const isUserEmail = email === data.userEmail;

    // Générer le lien approprié selon le destinataire
    const ticketUrl = isUserEmail
      ? `${baseUrl}/dashboard/support/ticket/${data.ticketId}`
      : `${baseUrl}/admin/support/ticket/${data.ticketId}`;

    // Icône adaptée au contexte
    let statusIcon = '🔄'; // Par défaut pour une mise à jour normale
    if (data.isAutomatic) {
      statusIcon = '⏱️'; // Pour une fermeture automatique
    } else if (data.isReopenedByComment) {
      statusIcon = '🔓'; // Pour une réouverture suite à un commentaire
    }

    // Titre adapté au contexte
    let statusTitle = 'Statut mis à jour';
    if (data.isAutomatic) {
      statusTitle = 'Fermeture automatique du ticket';
    } else if (data.isReopenedByComment) {
      statusTitle = 'Ticket réouvert suite à votre commentaire';
    }

    // Sujet de l'email adapté au contexte
    let emailSubject = 'Mise à jour du statut du ticket : ' + data.title;
    if (data.isAutomatic) {
      emailSubject = 'Fermeture automatique du ticket : ' + data.title;
    } else if (data.isReopenedByComment) {
      emailSubject = isUserEmail
        ? 'Ticket réouvert suite à votre commentaire : ' + data.title
        : 'Ticket réouvert par un utilisateur : ' + data.title;
    }

    const mailOptions = {
      from: `"Job Partiel" <${process.env.SMTP_FROM}>` || '<EMAIL>',
      to: email,
      subject: `${EMAIL_ICONS.supportTicketStatusChange} ${emailSubject}`,
      html: `
        <div style="font-family: Arial, sans-serif; width: 100%; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #FFF8F3 0%, #FFE4BA 100%); padding: 40px 0;">
          <div style="background-color: white; padding: 40px; border-radius: 16px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
            <div style="text-align: center; margin-bottom: 30px;">
              <img src="${baseUrl}/logo.png" alt="JobPartiel Logo" style="max-width: 150px; height: auto;">
            </div>

            <div style="text-align: center; margin-bottom: 30px;">
              <div style="display: inline-block; background-color: #FFF8F3; border-radius: 50%; width: 80px; height: 80px; line-height: 80px; text-align: center; margin-bottom: 15px;">
                <span style="font-size: 40px;">${statusIcon}</span>
              </div>
              <h2 style="color: #FF7A35; font-size: 28px; margin: 10px 0;">${statusTitle}</h2>
              <p style="color: #4B5563; font-size: 18px; margin: 0;">Ticket : ${data.title}</p>
            </div>

            <div style="background-color: #FFF8F3; padding: 25px; border-radius: 12px; margin: 30px 0;">
              <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 20px;">
                <div style="text-align: center; padding: 15px; background-color: white; border-radius: 8px; flex: 1;">
                  <p style="color: #6B7280; margin: 0 0 5px 0;">Ancien statut</p>
                  <p style="color: #374151; font-weight: bold; margin: 0;">${formatTicketStatus(data.oldStatus)}</p>
                </div>
                <div style="margin: 0 20px;">
                  <span style="font-size: 24px;">➔</span>
                </div>
                <div style="text-align: center; padding: 15px; background-color: white; border-radius: 8px; flex: 1;">
                  <p style="color: #6B7280; margin: 0 0 5px 0;">Nouveau statut</p>
                  <p style="color: #FF7A35; font-weight: bold; margin: 0;">${formatTicketStatus(data.newStatus)}</p>
                </div>
              </div>
              ${data.isAutomatic ? `
              <div style="background-color: rgba(255, 229, 186, 0.5); padding: 15px; border-radius: 8px; margin-top: 20px; border-left: 4px solid #FF965E;">
                <p style="color: #4B5563; margin: 0; font-size: 14px;">
                  <strong>Note :</strong> Ce ticket a été fermé automatiquement après 30 jours d'inactivité.
                  Si votre problème persiste, veuillez ouvrir un nouveau ticket.
                </p>
              </div>
              ` : ''}
              ${data.isReopenedByComment ? `
              <div style="background-color: rgba(255, 229, 186, 0.5); padding: 15px; border-radius: 8px; margin-top: 20px; border-left: 4px solid #FF965E;">
                <p style="color: #4B5563; margin: 0; font-size: 14px;">
                  <strong>Note :</strong> ${email === data.userEmail ?
                    `Votre ticket a été automatiquement réouvert suite à votre commentaire.
                    L'équipe de support a été notifiée et traitera votre demande dans les meilleurs délais.` :
                    `Ce ticket a été automatiquement réouvert suite à un commentaire de l'utilisateur.
                    Merci de prendre en charge cette demande dans les meilleurs délais.`}
                </p>
              </div>
              ` : ''}
            </div>

            <div style="text-align: center; margin-top: 30px;">
              <a href="${ticketUrl}"
                 style="display: inline-block; background-color: #FF7A35; color: white; text-decoration: none; padding: 12px 25px; border-radius: 8px; font-weight: bold;">
                Voir le ticket
              </a>
            </div>
          </div>

          <div style="text-align: center; margin-top: 20px; color: #6B7280; font-size: 14px;">
            <p style="margin: 5px 0;">© ${new Date().getFullYear()} JobPartiel - Tous droits réservés</p>
          </div>
        </div>
      `
    };

    await queueEmail(mailOptions.to, mailOptions.subject, mailOptions.html);
    logSecurity.info(LogEventType.EMAIL_SENT, 'Email de changement de statut de ticket de support envoyé', { email });
  } catch (error) {
    logSecurity.error(LogEventType.EMAIL_ERROR, 'Erreur lors de l\'envoi de l\'email de changement de statut de ticket de support', { email, error });
    throw error;
  }
};

export const sendSupportTicketAssignedEmail = async (email: string, data: {
  ticketId: string;
  title: string;
  assigneeEmail: string;
  assignerEmail: string;
  assignerName: string;
}) => {
  try {
    const baseUrl = process.env.FRONTEND_URL || 'http://localhost:5173';

    const mailOptions = {
      from: `"Job Partiel" <${process.env.SMTP_FROM}>` || '<EMAIL>',
      to: email,
      subject: `${EMAIL_ICONS.supportTicketAssigned} Ticket #${data.ticketId} vous a été assigné`,
      html: `
        <div style="font-family: Arial, sans-serif; width: 100%; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #FFF8F3 0%, #FFE4BA 100%); padding: 40px 0;">
          <div style="background-color: white; padding: 40px; border-radius: 16px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
            <div style="text-align: center; margin-bottom: 30px;">
              <img src="${baseUrl}/logo.png" alt="JobPartiel Logo" style="max-width: 150px; height: auto;">
            </div>

            <div style="text-align: center; margin-bottom: 30px;">
              <div style="display: inline-block; background-color: #FFF8F3; border-radius: 50%; width: 80px; height: 80px; line-height: 80px; text-align: center; margin-bottom: 15px;">
                <span style="font-size: 40px;">📋</span>
              </div>
              <h2 style="color: #FF7A35; font-size: 28px; margin: 10px 0;">Ticket assigné</h2>
              <p style="color: #4B5563; font-size: 18px; margin: 0;">Un ticket vous a été assigné</p>
            </div>

            <div style="background-color: #FFF8F3; padding: 25px; border-radius: 12px; margin: 30px 0;">
              <h3 style="color: #FF7A35; margin: 0 0 15px 0;">Détails du ticket :</h3>
              <p style="color: #374151; margin: 5px 0;"><strong>Titre :</strong> ${data.title}</p>
              <p style="color: #374151; margin: 5px 0;"><strong>Assigné par :</strong> ${data.assignerName} (${data.assignerEmail})</p>
            </div>

            <div style="text-align: center; margin-top: 30px;">
              <a href="${baseUrl}/support/tickets/${data.ticketId}"
                 style="display: inline-block; background-color: #FF7A35; color: white; text-decoration: none; padding: 12px 25px; border-radius: 8px; font-weight: bold;">
                Voir le ticket
              </a>
          </div>

          <div style="text-align: center; margin-top: 20px; color: #6B7280; font-size: 14px;">
            <p style="margin: 5px 0;">© ${new Date().getFullYear()} JobPartiel - Tous droits réservés</p>
          </div>
        </div>
      `
    };

    await queueEmail(mailOptions.to, mailOptions.subject, mailOptions.html);
    logSecurity.info(LogEventType.EMAIL_SENT, 'Email d\'assignation de ticket de support envoyé', { email });
  } catch (error) {
    logSecurity.error(LogEventType.EMAIL_ERROR, 'Erreur lors de l\'envoi de l\'email d\'assignation de ticket de support', { email, error });
    throw error;
  }
};

export const sendJobiExchangeEmail = async (
  expediteurEmail: string,
  destinataireEmail: string,
  montant: number,
  expediteurUsertag: string,
  destinataireUsertag: string,
  expediteurNomFormate?: string | null,
  destinataireNomFormate?: string | null,
  expediteurSoldeAvant?: number,
  expediteurSoldeApres?: number,
  destinataireSoldeAvant?: number,
  destinataireSoldeApres?: number,
  expediteurHistorique?: Array<{montant: number, date: string, description: string}>,
  destinataireHistorique?: Array<{montant: number, date: string, description: string}>,
  message?: string | null
) => {
  try {
    // Formater l'historique des transactions pour l'affichage
    const formatHistorique = (historique?: Array<{montant: number, date: string, description: string}>) => {
      if (!historique || historique.length === 0) return '';

      const formatDate = (dateStr: string) => {
        const date = new Date(dateStr);
        return date.toLocaleDateString('fr-FR', { day: '2-digit', month: '2-digit', year: 'numeric' });
      };

      // Prendre les 5 dernières transactions maximum
      const recentHistorique = historique.slice(0, 5);

      let html = `
        <div style="margin-top: 20px; margin-bottom: 20px;">
          <h3 style="color: #FF6B2C; font-size: 16px;">Dernières transactions</h3>
          <table style="width: 100%; border-collapse: collapse; font-size: 14px;">
            <tr style="background-color: #f9f9f9;">
              <th style="padding: 8px; text-align: left; border-bottom: 1px solid #ddd;">Date</th>
              <th style="padding: 8px; text-align: left; border-bottom: 1px solid #ddd;">Description</th>
              <th style="padding: 8px; text-align: right; border-bottom: 1px solid #ddd;">Montant</th>
            </tr>
      `;

      recentHistorique.forEach(transaction => {
        const montantStyle = transaction.montant >= 0
          ? 'color: #34D399;' // vert pour les montants positifs
          : 'color: #F87171;'; // rouge pour les montants négatifs

        html += `
          <tr>
            <td style="padding: 8px; border-bottom: 1px solid #ddd;">${formatDate(transaction.date)}</td>
            <td style="padding: 8px; border-bottom: 1px solid #ddd;">${transaction.description}</td>
            <td style="padding: 8px; border-bottom: 1px solid #ddd; text-align: right; ${montantStyle}">${transaction.montant >= 0 ? '+' : ''}${transaction.montant} Jobi</td>
          </tr>
        `;
      });

      html += `
        </table>
      </div>
      `;

      return html;
    };

    // Section pour afficher le message si présent
    const messageSection = message ? `
      <div style="background-color: white; border-radius: 8px; padding: 15px; margin-top: 20px; border: 1px solid #FFE4BA;">
        <h3 style="color: #FF6B2C; margin-top: 0; font-size: 16px;">Message</h3>
        <p style="margin-bottom: 0; color: #333; font-style: italic;">« ${message} »</p>
      </div>
    ` : '';

    // Générer des conseils aléatoires pour l'utilisation des Jobi
    const getConseilsJobi = () => {
      const conseils = [
        {
          titre: "Économisez vos Jobi",
          texte: "Pensez à garder une réserve de Jobi pour les missions que vous souhaitez vraiment obtenir."
        },
        {
          titre: "Offrez-vous des services",
          texte: "Utilisez vos Jobi pour obtenir des services de qualité auprès d'autres membres de la communauté."
        },
        {
          titre: "Complétez votre profil",
          texte: "Un profil complet attire davantage l'attention et augmente vos chances de recevoir des Jobi."
        },
        {
          titre: "Parrainez des amis",
          texte: "Pour chaque ami parrainé qui complète une mission, vous recevez des Jobi en récompense!"
        },
        {
          titre: "Proposez des missions de qualité",
          texte: "Des missions bien détaillées attirent des prestataires de qualité et optimisent l'utilisation de vos Jobi."
        }
      ];

      // Sélectionner 2 conseils aléatoires
      const selectedConseils: Array<{titre: string, texte: string}> = [];
      const indices = new Set<number>();

      while (indices.size < 2 && indices.size < conseils.length) {
        indices.add(Math.floor(Math.random() * conseils.length));
      }

      indices.forEach(index => {
        selectedConseils.push(conseils[index]);
      });

      let html = `
        <div style="margin-top: 25px; background-color: #FFF4E6; border-radius: 8px; padding: 15px;">
          <h3 style="color: #FF6B2C; margin-top: 0;">Conseils Jobi</h3>
      `;

      selectedConseils.forEach(conseil => {
        html += `
          <div style="margin-bottom: 10px;">
            <strong style="color: #FF6B2C;">${conseil.titre}</strong>
            <p style="margin-top: 5px; margin-bottom: 0; color: #666;">${conseil.texte}</p>
          </div>
        `;
      });

      html += `</div>`;

      return html;
    };

    // Email à l'expéditeur
    const expediteurMailOptions = {
      from: `"Job Partiel" <${process.env.SMTP_FROM}>` || '<EMAIL>',
      to: expediteurEmail,
      subject: `${EMAIL_ICONS.jobiExchangeConfirmation} Confirmation d'envoi de Jobi`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <img src="${process.env.FRONTEND_URL}/logo.png" alt="JobPartiel Logo" style="max-width: 150px;">
          </div>

          <div style="background-color: #FFF8F3; border-radius: 10px; padding: 20px; margin-bottom: 20px;">
            <h2 style="color: #FF6B2C; margin-bottom: 20px;">Confirmation de votre envoi de Jobi</h2>
            <p style="margin-bottom: 15px;">Bonjour ${expediteurNomFormate || `@${expediteurUsertag}`},</p>
            <p style="margin-bottom: 15px;">Nous vous confirmons l'envoi de <strong>${montant} Jobi</strong> à ${destinataireNomFormate ? `${destinataireNomFormate} (@${destinataireUsertag})` : `@${destinataireUsertag}`}.</p>
            <p style="margin-bottom: 15px;">Votre échange de Jobi a été effectué avec succès.</p>

            ${messageSection}

            ${expediteurSoldeAvant !== undefined && expediteurSoldeApres !== undefined ? `
            <div style="background-color: white; border-radius: 8px; padding: 15px; margin-top: 20px;">
              <h3 style="color: #FF6B2C; margin-top: 0; font-size: 16px;">Détails de l'échange de Jobi</h3>
              <table style="width: 100%;">
                <tr>
                  <td style="padding: 8px 0;">Solde avant échange :</td>
                  <td style="text-align: right; font-weight: bold;">${expediteurSoldeAvant} Jobi</td>
                </tr>
                <tr>
                  <td style="padding: 8px 0;">Montant envoyé :</td>
                  <td style="text-align: right; font-weight: bold; color: #F87171;">-${montant} Jobi</td>
                </tr>
                <tr style="border-top: 1px solid #eee;">
                  <td style="padding: 8px 0; font-weight: bold;">Solde actuel :</td>
                  <td style="text-align: right; font-weight: bold; color: #FF6B2C;">${expediteurSoldeApres} Jobi</td>
                </tr>
              </table>
            </div>
            ` : ''}
          </div>

          ${formatHistorique(expediteurHistorique)}

          ${getConseilsJobi()}

          <div style="text-align: center; margin-top: 30px;">
            <a href="${process.env.FRONTEND_URL}/dashboard/jobi"
               style="display: inline-block; background-color: #FF6B2C; color: white; text-decoration: none; padding: 12px 25px; border-radius: 8px; font-weight: bold;">
              Voir mon solde
            </a>
          </div>

          <div style="margin-top: 30px; padding: 15px; background-color: #f9f9f9; border-radius: 8px; font-size: 14px; color: #666;">
            <p style="margin-top: 0;">Vous pouvez utiliser vos Jobi pour :</p>
            <ul style="padding-left: 20px; margin-bottom: 0;">
              <li>Postuler à des missions intéressantes</li>
              <li>Récompenser des services exceptionnels</li>
              <li>Rémunérer d'autres membres de la communauté</li>
              <li>Créer des missions personnalisées avec l'IA</li>
              <li>Les échanger contre des crédits IA</li>
              <li>Et bientôt plus...</li>
            </ul>
          </div>

          <div style="text-align: center; margin-top: 20px; color: #6B7280; font-size: 14px;">
            <p style="margin: 5px 0;">© ${new Date().getFullYear()} JobPartiel - Tous droits réservés</p>
          </div>
        </div>
      `
    };

    // Email au destinataire
    const destinataireMailOptions = {
      from: `"Job Partiel" <${process.env.SMTP_FROM}>`,
      to: destinataireEmail,
      subject: `${EMAIL_ICONS.jobiExchangeReceived} Réception de Jobi`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <img src="${process.env.FRONTEND_URL}/logo.png" alt="JobPartiel Logo" style="max-width: 150px;">
          </div>

          <div style="background-color: #FFF8F3; border-radius: 10px; padding: 20px; margin-bottom: 20px;">
            <h2 style="color: #FF6B2C; margin-bottom: 20px;">Vous avez reçu des Jobi !</h2>
            <p style="margin-bottom: 15px;">Bonjour ${destinataireNomFormate || `@${destinataireUsertag}`},</p>
            <p style="margin-bottom: 15px;">Bonne nouvelle ! Vous avez reçu <strong>${montant} Jobi</strong> de la part de ${expediteurNomFormate ? `${expediteurNomFormate} (@${expediteurUsertag})` : `@${expediteurUsertag}`}.</p>
            <p style="margin-bottom: 15px;">Cette somme a été créditée sur votre compte et est disponible immédiatement.</p>

            ${messageSection}

            ${destinataireSoldeAvant !== undefined && destinataireSoldeApres !== undefined ? `
            <div style="background-color: white; border-radius: 8px; padding: 15px; margin-top: 20px;">
              <h3 style="color: #FF6B2C; margin-top: 0; font-size: 16px;">Détails de la transaction</h3>
              <table style="width: 100%;">
                <tr>
                  <td style="padding: 8px 0;">Solde avant transaction :</td>
                  <td style="text-align: right; font-weight: bold;">${destinataireSoldeAvant} Jobi</td>
                </tr>
                <tr>
                  <td style="padding: 8px 0;">Montant reçu :</td>
                  <td style="text-align: right; font-weight: bold; color: #34D399;">+${montant} Jobi</td>
                </tr>
                <tr style="border-top: 1px solid #eee;">
                  <td style="padding: 8px 0; font-weight: bold;">Solde actuel :</td>
                  <td style="text-align: right; font-weight: bold; color: #FF6B2C;">${destinataireSoldeApres} Jobi</td>
                </tr>
              </table>
            </div>
            ` : ''}
          </div>

          ${formatHistorique(destinataireHistorique)}

          ${getConseilsJobi()}

          <div style="text-align: center; margin-top: 30px;">
            <a href="${process.env.FRONTEND_URL}/dashboard/jobi"
               style="display: inline-block; background-color: #FF6B2C; color: white; text-decoration: none; padding: 12px 25px; border-radius: 8px; font-weight: bold;">
              Voir mon solde
            </a>
          </div>

          <div style="margin-top: 30px; padding: 15px; background-color: #f9f9f9; border-radius: 8px; font-size: 14px; color: #666;">
            <p style="margin-top: 0;">Que pouvez-vous faire avec vos Jobi ?</p>
            <ul style="padding-left: 20px; margin-bottom: 0;">
              <li>Proposer des missions et services à la communauté</li>
              <li>Répondre à des offres intéressantes</li>
              <li>Échanger avec d'autres utilisateurs pour des services</li>
            </ul>
            <p style="margin-top: 10px; margin-bottom: 0;">N'hésitez pas à explorer toutes les possibilités qu'offre JobPartiel !</p>
          </div>

          <div style="text-align: center; margin-top: 20px; color: #6B7280; font-size: 14px;">
            <p style="margin: 5px 0;">© ${new Date().getFullYear()} JobPartiel - Tous droits réservés</p>
          </div>
        </div>
      `
    };

    // Envoyer les emails séparément pour mieux déboguer
    try {
      await queueEmail(expediteurMailOptions.to, expediteurMailOptions.subject, expediteurMailOptions.html);
      logger.info('Email expéditeur envoyé avec succès', {
        destinataire: expediteurEmail,
        sujet: 'Confirmation d\'envoi de Jobi'
      });
    } catch (expediteurError) {
      logger.error('Erreur envoi email expéditeur:', expediteurError);
    }

    try {
      await queueEmail(destinataireMailOptions.to, destinataireMailOptions.subject, destinataireMailOptions.html);
      logger.info('Email destinataire envoyé avec succès', {
        destinataire: destinataireEmail,
        sujet: 'Réception de Jobi'
      });
    } catch (destinataireError) {
      logger.error('Erreur envoi email destinataire:', destinataireError);
    }

    logSecurity.info(LogEventType.EMAIL_SENT, 'Emails d\'échange de Jobi envoyés avec succès', {
      expediteur: expediteurUsertag,
      destinataire: destinataireUsertag,
      montant
    });
  } catch (error) {
    console.error('Erreur générale lors de l\'envoi des emails:', error);
    logSecurity.error(LogEventType.EMAIL_ERROR, 'Erreur lors de l\'envoi des emails d\'échange de Jobi', {
      error,
      expediteur: expediteurUsertag,
      destinataire: destinataireUsertag
    });
    throw new EmailServiceError(
      'Erreur lors de l\'envoi des emails d\'échange de Jobi',
      'JOBI_EXCHANGE_EMAIL_ERROR',
      { error }
    );
  }
};

export const sendNewMessageEmail = async (to: string, data: { senderName: string; messageUrl: string }) => {
  try {
    if (!isValidEmail(to)) {
      throw new EmailServiceError(
        EMAIL_ERRORS.INVALID_EMAIL.message(to),
        EMAIL_ERRORS.INVALID_EMAIL.code,
        { email: to }
      );
    }

    const mailOptions = {
      from: `"Job Partiel" <${process.env.SMTP_FROM}>` || '<EMAIL>',
      to,
      subject: `${EMAIL_ICONS.newMessage} ${EMAIL_SUBJECTS.newMessage}`,
      html: `
        <div style="font-family: 'Arial', sans-serif; width: 100%; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #FFF8F3 0%, #FFE4BA 100%); padding: 40px 0;">
          <div style="background-color: white; padding: 40px 20px; border-radius: 16px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
            <div style="text-align: center; margin-bottom: 30px;">
              <img src="https://jobpartiel.fr/assets/logos/logo.svg" alt="JobPartiel Logo" style="max-width: 150px; height: auto;">
            </div>

            <div style="text-align: center; background-color: #FFF8F3; padding: 20px; border-radius: 12px; margin-bottom: 30px;">
              <div style="background-color: #FF7A35; display: inline-block; border-radius: 50%; width: 80px; height: 80px; line-height: 80px; text-align: center; margin-bottom: 15px;">
                <span style="font-size: 40px; color: white;">💬</span>
              </div>
              <h2 style="color: #FF7A35; font-size: 28px; margin: 0 0 10px 0;">
                Nouveau message
              </h2>
              <p style="color: #374151; font-size: 16px; margin: 0;">
                De la part de <strong>${data.senderName}</strong>
              </p>
            </div>

            <p style="line-height: 1.8; color: #374151; font-size: 16px;">
              Bonjour,
            </p>

            <p style="line-height: 1.8; color: #374151; font-size: 16px;">
              Vous avez reçu un nouveau message sur JobPartiel. Connectez-vous pour le consulter et y répondre.
            </p>

            <div style="text-align: center; margin: 35px 0;">
              <a href="${data.messageUrl}"
                 style="display: inline-block;
                        background-color: #FF7A35;
                        color: white;
                        padding: 16px 40px;
                        text-decoration: none;
                        border-radius: 12px;
                        font-weight: 600;
                        font-size: 18px;
                        transition: all 0.3s ease;
                        box-shadow: 0 4px 6px rgba(255, 122, 53, 0.2);">
                Voir le message
              </a>
            </div>

            <div style="background-color: #FFF8F3; padding: 25px; border-radius: 12px; margin: 30px 0;">
              <h3 style="color: #FF7A35; margin: 0 0 15px 0; font-size: 20px;">
                <span style="margin-right: 10px;">💡</span>Conseils pour des échanges constructifs
              </h3>
              <ul style="color: #374151; font-size: 16px; line-height: 1.8; margin: 0; padding-left: 20px;">
                <li><strong>Prenez votre temps</strong> - Lisez attentivement le message avant de répondre</li>
                <li><strong>Restez professionnel</strong> - Privilégiez un ton courtois et respectueux</li>
                <li><strong>Soyez précis</strong> - Des réponses claires évitent les malentendus</li>
                <li><strong>Gardez une trace</strong> - Utilisez la messagerie de la plateforme pour vos échanges</li>
              </ul>
            </div>

            <div style="background-color: #FFF8F3; padding: 25px; border-radius: 12px; margin: 30px 0;">
              <h3 style="color: #FF7A35; margin: 0 0 15px 0; font-size: 20px;">
                <span style="margin-right: 10px;">⚠️</span>Points importants
              </h3>
              <ul style="color: #374151; font-size: 16px; line-height: 1.8; margin: 0; padding-left: 20px;">
                <li><strong>Sécurité avant tout</strong> - Ne partagez jamais vos informations bancaires</li>
                <li><strong>Restez sur la plateforme</strong> - Évitez les échanges de coordonnées personnelles</li>
                <li><strong>Signalez si nécessaire</strong> - Utilisez le bouton de signalement en cas de besoin</li>
              </ul>
            </div>

            <div style="text-align: center; color: #6B7280; font-size: 14px; margin-top: 40px;">
              <p>Si vous avez des questions, n'hésitez pas à contacter notre équipe de support.</p>
              <p>© ${new Date().getFullYear()} Job Partiel. Tous droits réservés.</p>
            </div>
          </div>
        </div>
        <style>
          @media only screen and (max-width: 600px) {
            h2 { font-size: 24px; }
            h3 { font-size: 18px; }
            p, ul { font-size: 14px; }
          }
        </style>
      `
    };

    await queueEmail(mailOptions.to, mailOptions.subject, mailOptions.html);

    logSecurity.info(LogEventType.EMAIL_SENT, 'Email de notification de nouveau message envoyé avec succès', { email: to });
  } catch (error) {
    if (error instanceof EmailServiceError) {
      throw error;
    }

    const errorMessage = error instanceof Error ? error.message : 'Une erreur inconnue est survenue';
    throw new EmailServiceError(
      EMAIL_ERRORS.SMTP_ERROR.message(errorMessage),
      EMAIL_ERRORS.SMTP_ERROR.code,
      { originalError: errorMessage }
    );
  }
};

export const sendNewConversationEmail = async (to: string, data: { senderName: string; conversationUrl: string }) => {
  try {
    if (!isValidEmail(to)) {
      throw new EmailServiceError(
        EMAIL_ERRORS.INVALID_EMAIL.message(to),
        EMAIL_ERRORS.INVALID_EMAIL.code,
        { email: to }
      );
    }

    const mailOptions = {
      from: `"Job Partiel" <${process.env.SMTP_FROM}>` || '<EMAIL>',
      to,
      subject: `${EMAIL_ICONS.newConversation} ${EMAIL_SUBJECTS.newConversation}`,
      html: `
        <div style="font-family: 'Arial', sans-serif; width: 100%; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #FFF8F3 0%, #FFE4BA 100%); padding: 40px 0;">
          <div style="background-color: white; padding: 40px 20px; border-radius: 16px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
            <div style="text-align: center; margin-bottom: 30px;">
              <img src="https://jobpartiel.fr/assets/logos/logo.svg" alt="JobPartiel Logo" style="max-width: 150px; height: auto;">
            </div>

            <div style="text-align: center; background-color: #FFF8F3; padding: 20px; border-radius: 12px; margin-bottom: 30px;">
              <div style="background-color: #FF7A35; display: inline-block; border-radius: 50%; width: 80px; height: 80px; line-height: 80px; text-align: center; margin-bottom: 15px;">
                <span style="font-size: 40px; color: white;">👋</span>
              </div>
              <h2 style="color: #FF7A35; font-size: 28px; margin: 0 0 10px 0;">
                Nouvelle conversation
              </h2>
              <p style="color: #374151; font-size: 16px; margin: 0;">
                <strong>${data.senderName}</strong> souhaite échanger avec vous
              </p>
            </div>

            <p style="line-height: 1.8; color: #374151; font-size: 16px;">
              Bonjour,
            </p>

            <p style="line-height: 1.8; color: #374151; font-size: 16px;">
              Une nouvelle conversation vient d'être démarrée sur JobPartiel. Connectez-vous pour découvrir le message et commencer l'échange.
            </p>

            <div style="text-align: center; margin: 35px 0;">
              <a href="${data.conversationUrl}"
                 style="display: inline-block;
                        background-color: #FF7A35;
                        color: white;
                        padding: 16px 40px;
                        text-decoration: none;
                        border-radius: 12px;
                        font-weight: 600;
                        font-size: 18px;
                        transition: all 0.3s ease;
                        box-shadow: 0 4px 6px rgba(255, 122, 53, 0.2);">
                Voir la conversation
              </a>
            </div>

            <div style="background-color: #FFF8F3; padding: 25px; border-radius: 12px; margin: 30px 0;">
              <h3 style="color: #FF7A35; margin: 0 0 15px 0; font-size: 20px;">
                <span style="margin-right: 10px;">💡</span>Conseils pour bien démarrer
              </h3>
              <ul style="color: #374151; font-size: 16px; line-height: 1.8; margin: 0; padding-left: 20px;">
                <li><strong>Présentez-vous</strong> - Une brève présentation aide à créer un climat de confiance</li>
                <li><strong>Soyez clair</strong> - Exprimez vos attentes et besoins de manière précise</li>
                <li><strong>Restez professionnel</strong> - Un ton courtois favorise les échanges constructifs</li>
                <li><strong>Soyez réactif</strong> - Une réponse rapide montre votre intérêt et votre sérieux</li>
              </ul>
            </div>

            <div style="background-color: #FFF8F3; padding: 25px; border-radius: 12px; margin: 30px 0;">
              <h3 style="color: #FF7A35; margin: 0 0 15px 0; font-size: 20px;">
                <span style="margin-right: 10px;">🤝</span>Les clés d'une collaboration réussie
              </h3>
              <ul style="color: #374151; font-size: 16px; line-height: 1.8; margin: 0; padding-left: 20px;">
                <li><strong>Respect mutuel</strong> - La base d'une relation professionnelle saine</li>
                <li><strong>Communication transparente</strong> - Privilégiez des échanges clairs et honnêtes</li>
                <li><strong>Confiance</strong> - Construisez une relation de confiance pas à pas</li>
                <li><strong>Bienveillance</strong> - Adoptez une attitude positive et constructive</li>
              </ul>
            </div>

            <div style="background-color: #FFF8F3; padding: 25px; border-radius: 12px; margin: 30px 0;">
              <h3 style="color: #FF7A35; margin: 0 0 15px 0; font-size: 20px;">
                <span style="margin-right: 10px;">⚠️</span>Points importants
              </h3>
              <ul style="color: #374151; font-size: 16px; line-height: 1.8; margin: 0; padding-left: 20px;">
                <li><strong>Sécurité avant tout</strong> - Ne partagez jamais vos informations bancaires</li>
                <li><strong>Restez sur la plateforme</strong> - Évitez les échanges de coordonnées personnelles</li>
                <li><strong>Signalez si nécessaire</strong> - Utilisez le bouton de signalement en cas de besoin</li>
                <li><strong>Gardez une trace</strong> - Utilisez la messagerie de la plateforme pour vos échanges</li>
              </ul>
            </div>

            <div style="text-align: center; color: #6B7280; font-size: 14px; margin-top: 40px;">
              <p>Si vous avez des questions, n'hésitez pas à contacter notre équipe de support.</p>
              <p>© ${new Date().getFullYear()} Job Partiel. Tous droits réservés.</p>
            </div>
          </div>
        </div>
        <style>
          @media only screen and (max-width: 600px) {
            h2 { font-size: 24px; }
            h3 { font-size: 18px; }
            p, ul { font-size: 14px; }
          }
        </style>
      `
    };

    await queueEmail(mailOptions.to, mailOptions.subject, mailOptions.html);

    logSecurity.info(LogEventType.EMAIL_SENT, 'Email de notification de nouvelle conversation envoyé avec succès', { email: to });
  } catch (error) {
    if (error instanceof EmailServiceError) {
      throw error;
    }

    const errorMessage = error instanceof Error ? error.message : 'Une erreur inconnue est survenue';
    throw new EmailServiceError(
      EMAIL_ERRORS.SMTP_ERROR.message(errorMessage),
      EMAIL_ERRORS.SMTP_ERROR.code,
      { originalError: errorMessage }
    );
  }
};

export const sendReviewReceivedEmail = async (email: string, data: {
  note: number;
  commentaire: string;
  qualites: string[];
  defauts?: string[];
  reviewerName?: string;
  is_modified?: boolean;
  reviewLink?: string;
}) => {
  try {
    const mailOptions = {
      from: `"Job Partiel" <${process.env.SMTP_FROM}>` || '<EMAIL>',
      to: email,
      subject: data.is_modified
        ? `${EMAIL_ICONS.reviewReceived} Un avis sur votre profil a été modifié`
        : `${EMAIL_ICONS.reviewReceived} ${EMAIL_SUBJECTS.reviewReceived}`,
      html: `
      <div style="font-family: 'Arial', sans-serif; width: 100%; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #FFF8F3 0%, #FFE4BA 100%); padding: 40px 0;">
        <div style="background-color: white; padding: 40px; border-radius: 16px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
          <div style="text-align: center; margin-bottom: 30px;">
            <img src="https://api.jobpartiel.fr/api/storage-proxy/photo_profil/avatar/avatar-defaut-jobpartiel.jpg" alt="Job Partiel" style="max-width: 100%; height: auto;">
          </div>

          <h2 style="color: #FF7A35; text-align: center; font-size: 28px; margin-bottom: 25px;">
            ${data.is_modified ? '📝 Un avis sur votre profil a été modifié' : '⭐ Nouvel avis reçu sur votre profil'}
          </h2>

          <div style="background-color: #FFF8F3; padding: 25px; border-radius: 12px; margin: 30px 0; border: 1px solid #FFE4BA;">
            <h3 style="color: #FF7A35; margin-bottom: 15px; font-size: 20px;">
              ${data.is_modified ? 'L\'avis modifié' : 'Un nouvel avis'} de ${data.reviewerName || 'un utilisateur'} :
            </h3>

            <div style="color: #374151; font-size: 16px; line-height: 1.8;">
              <div style="text-align: center; margin-bottom: 15px; font-size: 24px;">
                ${'⭐'.repeat(data.note)}
              </div>

              <div style="display: flex; align-items: center; margin-bottom: 15px;">
                <span style="font-size: 24px; margin-right: 10px;">👤</span>
                <p style="margin: 0;"><strong>De :</strong> ${data.reviewerName || 'Un utilisateur'}</p>
              </div>

              <div style="display: flex; align-items: center; margin-bottom: 15px;">
                <span style="font-size: 24px; margin-right: 10px;">⭐</span>
                <p style="margin: 0;"><strong>Note :</strong> ${data.note}/5</p>
              </div>

              ${data.qualites && data.qualites.length > 0 ? `
                <div style="display: flex; align-items: center; margin-bottom: 15px;">
                  <span style="font-size: 24px; margin-right: 10px;">🏷️</span>
                  <p style="margin: 0;"><strong>Qualités mises en avant :</strong> ${data.qualites.map(qualite => `<span style="display: inline-block; background-color: #FFE4BA; color: #FF7A35; padding: 4px 12px; border-radius: 16px; margin: 4px; font-size: 14px;">${qualite}</span>`).join('')}</p>
                </div>
              ` : ''}

              ${data.defauts && data.defauts.length > 0 ? `
                <div style="display: flex; align-items: center; margin-bottom: 15px;">
                  <span style="font-size: 24px; margin-right: 10px;">📊</span>
                  <p style="margin: 0;"><strong>Points à améliorer :</strong> ${data.defauts.map(defaut => `<span style="display: inline-block; background-color: #FEE2E2; color: #F43F5E; padding: 4px 12px; border-radius: 16px; margin: 4px; font-size: 14px;">${defaut}</span>`).join('')}</p>
                </div>
              ` : ''}

              ${data.commentaire ? `
                <div style="display: flex; align-items: flex-start; margin-bottom: 15px;">
                  <span style="font-size: 24px; margin-right: 10px;">💬</span>
                  <div style="flex: 1;">
                    <p style="margin: 0 0 5px 0;"><strong>Commentaire :</strong></p>
                    <p style="font-style: italic; margin: 0; color: #4B5563;">${data.commentaire}</p>
                  </div>
                </div>
              ` : ''}
            </div>
          </div>

          <div style="text-align: center; margin: 35px 0;">
            <a href="${process.env.FRONTEND_URL}${data.reviewLink || '/dashboard/avis'}"
               style="display: inline-block;
                      background-color: #FF7A35;
                      color: white;
                      padding: 16px 40px;
                      text-decoration: none;
                      border-radius: 12px;
                      font-weight: 600;
                      font-size: 18px;
                      transition: all 0.3s ease;
                      box-shadow: 0 4px 6px rgba(255, 122, 53, 0.2);">
              Voir l'avis
            </a>
          </div>

          <div style="text-align: center; margin-top: 30px; padding-top: 30px; border-top: 2px solid #FFE4BA;">
            <p style="color: #4B5563; font-size: 14px; margin-bottom: 10px;">
              Merci de contribuer à la qualité de notre plateforme !
            </p>
          </div>
        </div>

        <div style="text-align: center; margin-top: 15px; color: #666; font-size: 12px;">
          © ${new Date().getFullYear()} Job Partiel. Tous droits réservés.
        </div>
      </div>
      <style>
        @media only screen and (max-width: 600px) {
          h2 { font-size: 24px; }
          h3 { font-size: 18px; }
          p { font-size: 14px; }
        }
      </style>
      `
    };

    await queueEmail(mailOptions.to, mailOptions.subject, mailOptions.html);
    logSecurity.info(LogEventType.EMAIL_SENT, 'Email de nouvel avis envoyé avec succès', { to: email });
  } catch (error) {
    logSecurity.error(LogEventType.EMAIL_ERROR, 'Erreur lors de l\'envoi de l\'email de nouvel avis', { error, email });
    throw error;
  }
};

export const sendReviewPostedEmail = async (email: string, data: {
  note: number;
  commentaire: string;
  qualites: string[];
  defauts?: string[];
  jobi_earned: number;
  targetUserName?: string;
  is_modified?: boolean;
  reviewLink?: string;
}) => {
  try {
    const mailOptions = {
      from: `"Job Partiel" <${process.env.SMTP_FROM}>` || '<EMAIL>',
      to: email,
      subject: data.is_modified
        ? `${EMAIL_ICONS.reviewPosted} Votre avis a été modifié avec succès`
        : `${EMAIL_ICONS.reviewPosted} ${EMAIL_SUBJECTS.reviewPosted}`,
      html: `
      <div style="font-family: 'Arial', sans-serif; width: 100%; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #FFF8F3 0%, #FFE4BA 100%); padding: 40px 0;">
        <div style="background-color: white; padding: 40px; border-radius: 16px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
          <div style="text-align: center; margin-bottom: 30px;">
            <img src="https://api.jobpartiel.fr/api/storage-proxy/photo_profil/avatar/avatar-defaut-jobpartiel.jpg" alt="Job Partiel" style="max-width: 100%; height: auto;">
          </div>

          <h2 style="color: #FF7A35; text-align: center; font-size: 28px; margin-bottom: 25px;">
            ${data.is_modified ? '📝 Votre avis a été modifié avec succès' : '📝 Votre avis a été publié avec succès'}
          </h2>

          <div style="background-color: #FFF8F3; padding: 25px; border-radius: 12px; margin: 30px 0; border: 1px solid #FFE4BA;">
            <h3 style="color: #FF7A35; margin-bottom: 15px; font-size: 20px;">
              Récapitulatif de votre avis ${data.is_modified ? 'modifié' : 'publié'} pour ${data.targetUserName || 'l\'utilisateur'} :
            </h3>

            <div style="color: #374151; font-size: 16px; line-height: 1.8;">
              <div style="text-align: center; margin-bottom: 15px; font-size: 24px;">
                ${'⭐'.repeat(data.note)}
              </div>

              <div style="display: flex; align-items: center; margin-bottom: 15px;">
                <p style="margin: 0;"><strong>Note attribuée :</strong> ${data.note}/5</p>
              </div>

              ${data.commentaire ? `
                <p style="margin-bottom: 15px;">
                  <strong>Votre commentaire :</strong><br>
                  ${data.commentaire}
                </p>
              ` : ''}

              ${data.qualites && data.qualites.length > 0 ? `
                <p style="margin-bottom: 15px;">
                  <strong>Qualités mises en avant :</strong><br>
                  ${data.qualites.map(type => `<span style="display: inline-block; background-color: #FFE4BA; color: #FF7A35; padding: 4px 12px; border-radius: 16px; margin: 4px; font-size: 14px;">${type}</span>`).join('')}
                </p>
              ` : ''}

              ${data.defauts && data.defauts.length > 0 ? `
                <p style="margin-bottom: 15px;">
                  <strong>Points à améliorer :</strong><br>
                  ${data.defauts.map(defaut => `<span style="display: inline-block; background-color: #FEE2E2; color: #F43F5E; padding: 4px 12px; border-radius: 16px; margin: 4px; font-size: 14px;">${defaut}</span>`).join('')}
                </p>
              ` : ''}
            </div>
          </div>

          ${!data.is_modified ? `
          <div style="background-color: #FFF8F3; padding: 20px; border-radius: 12px; margin: 20px 0; border: 1px solid #FF7A35;">
            <h4 style="color: #FF7A35; margin-bottom: 15px; font-size: 18px;">
              🎉 Récompense reçue !
            </h4>
            <p style="color: #374151; font-size: 16px; line-height: 1.6;">
              Pour votre contribution à la communauté, vous avez reçu <strong>${data.jobi_earned} Jobi</strong> !
            </p>
          </div>
          ` : ''}

          <div style="text-align: center; margin: 35px 0;">
            <a href="${process.env.FRONTEND_URL}${data.reviewLink || '/dashboard/avis'}"
               style="display: inline-block;
                      background-color: #FF7A35;
                      color: white;
                      padding: 16px 40px;
                      text-decoration: none;
                      border-radius: 12px;
                      font-weight: 600;
                      font-size: 18px;
                      transition: all 0.3s ease;
                      box-shadow: 0 4px 6px rgba(255, 122, 53, 0.2);">
              Voir l'avis
            </a>
          </div>
        </div>
      </div>
      <style>
        @media only screen and (max-width: 600px) {
          h2 { font-size: 24px; }
          h3 { font-size: 18px; }
          p { font-size: 14px; }
        }
      </style>
      `
    };

    await queueEmail(mailOptions.to, mailOptions.subject, mailOptions.html);
  } catch (error) {
    logger.error('Erreur lors de l\'envoi de l\'email de confirmation d\'avis:', error);
    throw error;
  }
};

export const sendReviewReplyEmail = async (email: string, data: {
  reponse: string;
  review: {
    note: number;
    commentaire: string;
    qualites: string[];
    defauts?: string[];
  };
  responderName?: string;
  reviewLink?: string;
}) => {
  try {
    const mailOptions = {
      from: `"Job Partiel" <${process.env.SMTP_FROM}>` || '<EMAIL>',
      to: email,
      subject: `${EMAIL_ICONS.reviewReply} ${EMAIL_SUBJECTS.reviewReply}`,
      html: `
      <div style="font-family: 'Arial', sans-serif; width: 100%; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #FFF8F3 0%, #FFE4BA 100%); padding: 40px 0;">
        <div style="background-color: white; padding: 40px; border-radius: 16px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
          <div style="text-align: center; margin-bottom: 30px;">
            <img src="https://api.jobpartiel.fr/api/storage-proxy/photo_profil/avatar/avatar-defaut-jobpartiel.jpg" alt="Job Partiel" style="max-width: 100%; height: auto;">
          </div>

          <h2 style="color: #FF7A35; text-align: center; font-size: 28px; margin-bottom: 25px;">
            💬 Une réponse à votre avis
          </h2>

          <div style="background-color: #FFF8F3; padding: 25px; border-radius: 12px; margin: 30px 0; border: 1px solid #FFE4BA;">
            <h3 style="color: #FF7A35; margin-bottom: 15px; font-size: 20px;">
              ${data.responderName || 'L\'utilisateur'} a répondu à votre avis :
            </h3>

            <div style="background-color: #F9FAFB; padding: 20px; border-radius: 12px; margin: 20px 0;">
              <p style="color: #374151; font-style: italic; margin-top: 0; line-height: 1.6;">
                "${data.reponse}"
              </p>
              <p style="margin-bottom: 0; text-align: right; font-size: 14px; color: #6B7280;">
                — ${data.responderName || 'L\'utilisateur'}
              </p>
            </div>

            <div style="margin-top: 25px; border-top: 1px solid #E5E7EB; padding-top: 20px;">
              <h4 style="color: #4B5563; margin-bottom: 15px; font-size: 16px;">
                Votre avis initial :
              </h4>

              <div style="margin-left: 15px; color: #4B5563; line-height: 1.6;">
                <p style="margin-bottom: 10px;">
                  <strong>Note :</strong> ${data.review.note}/5 ${'⭐'.repeat(data.review.note)}
                </p>

                ${data.review.commentaire ? `
                  <p style="margin-bottom: 10px;">
                    <strong>Commentaire :</strong> "${data.review.commentaire}"
                  </p>
                ` : ''}

                ${data.review.qualites && data.review.qualites.length > 0 ? `
                  <p style="margin-bottom: 10px;">
                    <strong>Qualités mises en avant :</strong><br>
                    ${data.review.qualites.map(type => `<span style="display: inline-block; background-color: #FFE4BA; color: #FF7A35; padding: 4px 12px; border-radius: 16px; margin: 4px; font-size: 14px;">${type}</span>`).join('')}
                  </p>
                ` : ''}

                ${data.review.defauts && data.review.defauts.length > 0 ? `
                  <p style="margin-bottom: 10px;">
                    <strong>Points à améliorer :</strong><br>
                    ${data.review.defauts.map(defaut => `<span style="display: inline-block; background-color: #FEE2E2; color: #F43F5E; padding: 4px 12px; border-radius: 16px; margin: 4px; font-size: 14px;">${defaut}</span>`).join('')}
                  </p>
                ` : ''}
              </div>
            </div>
          </div>

          <div style="text-align: center; margin: 35px 0;">
            <a href="${process.env.FRONTEND_URL}${data.reviewLink || '/dashboard/avis'}"
               style="display: inline-block;
                      background-color: #FF7A35;
                      color: white;
                      padding: 16px 40px;
                      text-decoration: none;
                      border-radius: 12px;
                      font-weight: 600;
                      font-size: 18px;
                      transition: all 0.3s ease;
                      box-shadow: 0 4px 6px rgba(255, 122, 53, 0.2);">
              Voir la réponse
            </a>
          </div>
        </div>

        <div style="text-align: center; margin-top: 15px; color: #666; font-size: 12px;">
          © ${new Date().getFullYear()} Job Partiel. Tous droits réservés.
        </div>
      </div>
      <style>
        @media only screen and (max-width: 600px) {
          h2 { font-size: 24px; }
          h3 { font-size: 18px; }
          p { font-size: 14px; }
        }
      </style>
      `
    };

    await queueEmail(mailOptions.to, mailOptions.subject, mailOptions.html);
  } catch (error) {
    logger.error('Erreur lors de l\'envoi de l\'email de réponse à l\'avis:', error);
    throw error;
  }
};

export const sendReviewDeletedEmail = async (email: string, data: {
  reviewId: string;
  note: number;
  commentaire: string;
  qualites: string[];
  defauts?: string[];
  targetUserName?: string;
  authorName?: string;
}) => {
  try {
    const mailOptions = {
      from: `"Job Partiel" <${process.env.SMTP_FROM}>` || '<EMAIL>',
      to: email,
      subject: `${EMAIL_ICONS.reviewDeleted} ${EMAIL_SUBJECTS.reviewDeleted}`,
      html: `
      <div style="font-family: 'Arial', sans-serif; width: 100%; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #FFF8F3 0%, #FFE4BA 100%); padding: 40px 0;">
        <div style="background-color: white; padding: 40px; border-radius: 16px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
          <div style="text-align: center; margin-bottom: 30px;">
            <img src="https://api.jobpartiel.fr/api/storage-proxy/photo_profil/avatar/avatar-defaut-jobpartiel.jpg" alt="Job Partiel" style="max-width: 100%; height: auto;">
          </div>

          <h2 style="color: #FF7A35; text-align: center; font-size: 28px; margin-bottom: 25px;">
            🗑 Un avis a été supprimé
          </h2>

          <div style="background-color: #FFF8F3; padding: 25px; border-radius: 12px; margin: 30px 0;">
            <h3 style="color: #FF7A35; margin-bottom: 15px; font-size: 20px;">
              Détails de l'avis supprimé ${data.authorName ? `de ${data.authorName}` : ''} :
            </h3>

            <div style="color: #374151; font-size: 16px; line-height: 1.8;">
              <div style="display: flex; align-items: center; margin-bottom: 15px;">
                <p style="margin: 0;"><strong>Note :</strong> ${data.note}/5</p>
              </div>

              ${data.commentaire ? `
                <p style="margin-bottom: 15px;">
                  <strong>Commentaire :</strong><br>
                  ${data.commentaire}
                </p>
              ` : ''}

              ${data.qualites && data.qualites.length > 0 ? `
                <p style="margin-bottom: 15px;">
                  <strong>Qualités mises en avant :</strong><br>
                  ${data.qualites.join(', ')}
                </p>
              ` : ''}

              ${data.defauts && data.defauts.length > 0 ? `
                <p style="margin-bottom: 15px;">
                  <strong>Points à améliorer :</strong><br>
                  ${data.defauts.join(', ')}
                </p>
              ` : ''}
            </div>
          </div>

          <div style="background-color: #FFF8F3; padding: 20px; border-radius: 12px; margin: 20px 0;">
            <p style="color: #374151; font-size: 16px; line-height: 1.6;">
              Si vous avez des questions concernant cette suppression, n'hésitez pas à contacter notre support.
            </p>
          </div>

          <div style="text-align: center; margin: 35px 0;">
            <a href="https://jobpartiel.fr/dashboard/support"
               style="display: inline-block;
                      background-color: #FF7A35;
                      color: white;
                      padding: 16px 40px;
                      text-decoration: none;
                      border-radius: 12px;
                      font-weight: 600;
                      font-size: 18px;
                      transition: all 0.3s ease;
                      box-shadow: 0 4px 6px rgba(255, 122, 53, 0.2);">
              Contacter le support
            </a>
          </div>
        </div>
      </div>
      <style>
        @media only screen and (max-width: 600px) {
          h2 { font-size: 24px; }
          h3 { font-size: 18px; }
          p { font-size: 14px; }
        }
      </style>
      `
    };

    await queueEmail(mailOptions.to, mailOptions.subject, mailOptions.html);
  } catch (error) {
    logger.error('Erreur lors de l\'envoi de l\'email de suppression d\'avis:', error);
    throw error;
  }
};

export const sendPaymentConfirmedEmail = async (email: string, data: {
  amount: number;
  totalAmount: number;
  isFullPayment: boolean;
  missionTitle: string;
  missionUrl: string;
  paymentMethod: string;
  jobbeurName: string;
  clientName: string;
}) => {
  try {
    if (!isValidEmail(email)) {
      throw new EmailServiceError(
        EMAIL_ERRORS.INVALID_EMAIL.message(email),
        EMAIL_ERRORS.INVALID_EMAIL.code,
        { email }
      );
    }

    const mailOptions = {
      from: `"Job Partiel" <${process.env.SMTP_FROM}>` || '<EMAIL>',
      to: email,
      subject: EMAIL_SUBJECTS.paymentReceived,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <img src="https://jobpartiel.fr/assets/logos/logo.svg" alt="JobPartiel Logo" style="max-width: 200px;">
          </div>

          <div style="background: #FFF8F3; border-radius: 10px; padding: 30px; margin-bottom: 30px; border: 1px solid rgba(255, 107, 44, 0.1);">
            <div style="text-align: center; margin-bottom: 25px;">
              <div style="background-color: rgba(255, 107, 44, 0.1); width: 60px; height: 60px; border-radius: 50%; display: inline-flex; align-items: center; justify-content: center; margin-bottom: 15px;">
                <span style="font-size: 30px;">💸</span>
              </div>
              <h2 style="color: #FF6B2C; margin: 0; font-size: 24px;">Paiement ${data.isFullPayment ? 'complet' : 'partiel'} reçu</h2>
            </div>

            <p style="color: #2D3748; font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
              ${data.jobbeurName} a confirmé vous avoir envoyé un paiement manuel ${data.isFullPayment ? '(complet)' : '(partiel)'} de <strong style="color: #FF6B2C;">${data.amount}€</strong>${!data.isFullPayment ? ` (sur un total de ${data.totalAmount}€)` : ''} pour la mission "${data.missionTitle}".
            </p>

            <div style="background: white; border-radius: 8px; padding: 15px; margin-bottom: 20px; border: 1px solid rgba(255, 107, 44, 0.1);">
              <p style="margin: 0; color: #4A5568;">
                <strong>Méthode de paiement :</strong>
                <span style="color: #FF6B2C;">${data.paymentMethod}</span>
              </p>
            </div>

            <div style="text-align: center;">
              <a href="${data.missionUrl}" style="display: inline-block; background-color: #FF6B2C; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; font-weight: bold; margin-top: 10px;">
                Voir les détails de la mission
              </a>
            </div>
          </div>

          <div style="text-align: center; color: #718096; font-size: 14px;">
            <p style="margin-bottom: 10px;">Cordialement,</p>
            <p style="margin: 0;">L'équipe JobPartiel</p>
          </div>

          <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #E2E8F0; font-size: 12px; color: #A0AEC0; text-align: center;">
            <p>&copy; ${new Date().getFullYear()} Job Partiel. Tous droits réservés.</p>
          </div>
        </div>
      `,
    };

    await queueEmail(mailOptions.to, mailOptions.subject, mailOptions.html);
    logSecurity.info(LogEventType.EMAIL_SENT, 'Email de notification de paiement reçu envoyé', { to: email });
  } catch (error) {
    logger.error('Erreur lors de l\'envoi de l\'email de paiement reçu:', error);
    throw error;
  }
};

export const sendPaymentReceivedEmail = async (email: string, data: {
  amount: number;
  totalAmount: number;
  isFullPayment: boolean;
  missionTitle: string;
  missionUrl: string;
  paymentMethod: string;
  clientName: string;
  jobbeurName: string;
}) => {
  try {
    if (!isValidEmail(email)) {
      throw new EmailServiceError(
        EMAIL_ERRORS.INVALID_EMAIL.message(email),
        EMAIL_ERRORS.INVALID_EMAIL.code,
        { email }
      );
    }

    const mailOptions = {
      from: `"Job Partiel" <${process.env.SMTP_FROM}>` || '<EMAIL>',
      to: email,
      subject: EMAIL_SUBJECTS.paymentConfirmed,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <img src="https://jobpartiel.fr/assets/logos/logo.svg" alt="JobPartiel Logo" style="max-width: 200px;">
          </div>

          <div style="background: #FFF8F3; border-radius: 10px; padding: 30px; margin-bottom: 30px; border: 1px solid rgba(255, 107, 44, 0.1);">
            <div style="text-align: center; margin-bottom: 25px;">
              <div style="background-color: rgba(255, 107, 44, 0.1); width: 60px; height: 60px; border-radius: 50%; display: inline-flex; align-items: center; justify-content: center; margin-bottom: 15px;">
                <span style="font-size: 30px;">💰</span>
              </div>
              <h2 style="color: #FF6B2C; margin: 0; font-size: 24px;">Paiement ${data.isFullPayment ? 'complet' : 'partiel'} confirmé</h2>
            </div>

            <p style="color: #2D3748; font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
              Votre paiement manuel ${data.isFullPayment ? '(complet)' : '(partiel)'} de <strong style="color: #FF6B2C;">${data.amount}€</strong>${!data.isFullPayment ? ` (sur un total de ${data.totalAmount}€)` : ''} pour la mission "${data.missionTitle}" a été enregistré.
            </p>

            <div style="background: white; border-radius: 8px; padding: 15px; margin-bottom: 20px; border: 1px solid rgba(255, 107, 44, 0.1);">
              <p style="margin: 0 0 10px 0; color: #4A5568;">
                <strong>Méthode de paiement :</strong>
                <span style="color: #FF6B2C;">${data.paymentMethod}</span>
              </p>
              <p style="margin: 0; color: #4A5568;">
                <strong>Jobbeur :</strong>
                <span style="color: #FF6B2C;">${data.clientName}</span>
              </p>
            </div>

            <div style="text-align: center;">
              <a href="${data.missionUrl}" style="display: inline-block; background-color: #FF6B2C; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; font-weight: bold; margin-top: 10px;">
                Voir les détails de la mission
              </a>
            </div>
          </div>

          <div style="text-align: center; color: #718096; font-size: 14px;">
            <p style="margin-bottom: 10px;">Cordialement,</p>
            <p style="margin: 0;">L'équipe JobPartiel</p>
          </div>

          <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #E2E8F0; font-size: 12px; color: #A0AEC0; text-align: center;">
            <p>&copy; ${new Date().getFullYear()} Job Partiel. Tous droits réservés.</p>
          </div>
        </div>
      `,
    };

    await queueEmail(mailOptions.to, mailOptions.subject, mailOptions.html);
    logSecurity.info(LogEventType.EMAIL_SENT, 'Email de confirmation de paiement envoyé', { to: email });
  } catch (error) {
    logger.error('Erreur lors de l\'envoi de l\'email de confirmation de paiement:', error);
    throw error;
  }
};

export const sendNewBadgesEmail = async (email: string, data: {
  badges: Array<{
    badge_id: string;
    title: string;
    description?: string;
    detailedDescription?: string;
    recompense_jobi: number;
    is_lifetime: boolean;
  }>;
  lostBadges?: Array<{
    badge_id: string;
    title: string;
    description?: string;
    recompense_jobi: number;
    is_lifetime: boolean;
  }>;
  totalJobi: number;
  username?: string;
}) => {
  try {
    if (!isValidEmail(email)) {
      throw new EmailServiceError(
        EMAIL_ERRORS.INVALID_EMAIL.message(email),
        EMAIL_ERRORS.INVALID_EMAIL.code,
        { email }
      );
    }

    // Vérifier s'il y a des badges perdus
    const hasLostBadges = data.lostBadges && data.lostBadges.length > 0;
    const hasNewBadges = data.badges && data.badges.length > 0;

    // Préparer le sujet de l'email en fonction du contenu
    let subject;
    if (hasNewBadges && hasLostBadges) {
      subject = `${EMAIL_ICONS.new_badges} Mise à jour de vos badges sur JobPartiel`;
    } else if (hasNewBadges) {
      subject = `${EMAIL_ICONS.new_badges} Félicitations ! Vous avez obtenu ${data.badges.length > 1 ? 'de nouveaux badges' : 'un nouveau badge'} sur JobPartiel`;
    } else {
      subject = `${EMAIL_ICONS.new_badges} Information importante concernant vos badges JobPartiel`;
    }

    // Titre de l'email
    let emailTitle;
    if (hasNewBadges && hasLostBadges) {
      emailTitle = 'Mise à jour de vos badges';
    } else if (hasNewBadges) {
      emailTitle = data.badges.length > 1 ? 'Nouveaux Badges Obtenus !' : 'Nouveau Badge Obtenu !';
    } else {
      emailTitle = 'Badges perdus';
    }

    // Préparer le HTML des badges perdus
    const lostBadgesHtml = hasLostBadges
      ? `
        <div style="margin-bottom: 25px;">
          <h3 style="color: #FF0000; margin-bottom: 15px; border-bottom: 1px solid #FFE4BA; padding-bottom: 8px;">
            ${data.lostBadges!.length > 1 ? 'Badges perdus :' : 'Badge perdu :'}
          </h3>

          ${data.lostBadges!.map(badge => `
            <div style="padding: 15px; margin-bottom: 15px; background-color: #FFF8F3; border-radius: 8px; border-left: 4px solid #FF0000;">
              <h4 style="color: #FF0000; margin-top: 0; margin-bottom: 10px; font-size: 18px;">
                ${badge.title}${badge.is_lifetime ? ' <span style="background-color: #FFE4BA; color: #FF0000; padding: 2px 6px; border-radius: 4px; font-size: 14px; margin-left: 5px;">à vie</span>' : ''}
              </h4>
              <p style="margin: 0 0 10px 0; color: #555;">
                ${badge.description || ''}
              </p>
              <p style="margin: 0; color: #FF0000; font-weight: bold;">
                Jobi retirés : ${badge.recompense_jobi} Jobi
              </p>
            </div>
          `).join('')}
        </div>
      `
      : '';

    // Message pour les badges perdus
    const lostBadgesMessage = hasLostBadges
      ? `<p style="font-size: 16px; color: #333; line-height: 1.5; margin-bottom: 20px;">
          Nous sommes désolés de vous informer que vous ne remplissez plus les conditions requises pour
          ${data.lostBadges!.length > 1 ? 'certains badges' : 'un badge'} sur JobPartiel.
        </p>`
      : '';

    // Message pour les nouveaux badges
    const newBadgesMessage = hasNewBadges
      ? `<p style="font-size: 16px; color: #333; line-height: 1.5; margin-bottom: 20px;">
          Félicitations ! Vous venez d'obtenir ${data.badges.length > 1 ? 'de nouveaux badges' : 'un nouveau badge'} sur JobPartiel,
          reflétant votre engagement et vos réalisations sur notre plateforme.
        </p>`
      : '';

    // Section des nouveaux badges
    const newBadgesSection = hasNewBadges
      ? `<div style="margin-bottom: 25px;">
          <h3 style="color: #FF6B2C; margin-bottom: 15px; border-bottom: 1px solid #FFE4BA; padding-bottom: 8px;">
            ${data.badges.length > 1 ? 'Vos nouveaux badges :' : 'Votre nouveau badge :'}
          </h3>

          ${data.badges.map(badge => `
            <div style="padding: 15px; margin-bottom: 15px; background-color: #FFF8F3; border-radius: 8px; border-left: 4px solid #FF6B2C;">
              <h4 style="color: #FF6B2C; margin-top: 0; margin-bottom: 10px; font-size: 18px;">
                ${badge.title}${badge.is_lifetime ? ' <span style="background-color: #FFE4BA; color: #FF6B2C; padding: 2px 6px; border-radius: 4px; font-size: 14px; margin-left: 5px;">à vie</span>' : ''}
              </h4>
              <p style="margin: 0 0 10px 0; color: #555;">
                ${badge.description || ''}
              </p>
              <p style="margin: 0; color: #FF7A35; font-weight: bold;">
                Récompense : ${badge.recompense_jobi} Jobi
              </p>
            </div>
          `).join('')}
        </div>`
      : '';

    // Message pour les Jobi
    const jobiMessage = data.totalJobi >= 0
      ? `<div style="background-color: #FFE4BA; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
          <p style="margin: 0; color: #FF6B2C; font-weight: bold; text-align: center;">
            Total des Jobi gagnés : ${data.totalJobi} Jobi
          </p>
        </div>
        <p style="font-size: 16px; color: #333; line-height: 1.5; margin-bottom: 20px;">
          Ces Jobi ont été automatiquement ajoutés à votre solde.
          Continuez votre excellent travail sur JobPartiel pour gagner encore plus de badges !
        </p>`
      : `<div style="background-color: #FFF0F0; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
          <p style="margin: 0; color: #FF0000; font-weight: bold; text-align: center;">
            Total des Jobi retirés : ${Math.abs(data.totalJobi)} Jobi
          </p>
        </div>
        <p style="font-size: 16px; color: #333; line-height: 1.5; margin-bottom: 20px;">
          Ces Jobi ont été automatiquement retirés de votre solde suite à la perte de certains badges.
          Continuez à participer activement pour récupérer ces badges et gagner des Jobi !
        </p>`;

    const mailOptions = {
      from: `"Job Partiel" <${process.env.SMTP_FROM}>` || '<EMAIL>',
      to: email,
      subject: subject,
      html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #FFF8F3; border-radius: 10px;">
        <div style="text-align: center; margin-bottom: 20px;">
          <img src="https://supabase.jobpartiel.fr/storage/v1/object/public/email_assets/logo.png" alt="JobPartiel Logo" style="max-width: 150px;">
        </div>

        <div style="background-color: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
          <h2 style="color: #FF6B2C; text-align: center; margin-bottom: 20px;">
            ${emailTitle}
          </h2>

          <p style="font-size: 16px; color: #333; line-height: 1.5; margin-bottom: 20px;">
            ${data.username ? `Bonjour ${data.username},` : 'Bonjour,'}
          </p>

          ${newBadgesMessage}
          ${lostBadgesMessage}

          ${newBadgesSection}
          ${lostBadgesHtml}

          ${jobiMessage}

          <div style="text-align: center; margin-top: 30px;">
            <a href="${process.env.FRONTEND_URL || 'https://jobpartiel.fr'}/dashboard/badges"
               style="background-color: #FF6B2C; color: white; padding: 12px 25px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
              Voir Mes Badges
            </a>
          </div>
        </div>

        <div style="text-align: center; margin-top: 20px; font-size: 12px; color: #777;">
          <p>
            © ${new Date().getFullYear()} JobPartiel - Tous droits réservés
          </p>
          <p>
            Cet email a été envoyé automatiquement. Merci de ne pas y répondre.
          </p>
        </div>
      </div>
      <style>
        @media only screen and (max-width: 600px) {
          h2 { font-size: 24px; }
          h3 { font-size: 18px; }
          p { font-size: 14px; }
        }
      </style>
      `
    };

    await queueEmail(mailOptions.to, mailOptions.subject, mailOptions.html);

    logSecurity.info(LogEventType.EMAIL_SENT, 'Notification email envoyé pour badges', {
      username: data.username,
      newBadgesCount: hasNewBadges ? data.badges.length : 0,
      lostBadgesCount: hasLostBadges ? data.lostBadges!.length : 0,
      totalJobi: data.totalJobi
    });
  } catch (error) {
    logger.error('Erreur lors de l\'envoi de l\'email de badges:', error);
    throw error;
  }
};

// Interface pour les données d'email de facture
interface InvoiceEmailData {
  subject: string;
  html: string;
  attachments: Array<{
    filename: string;
    path: string;
  }>;
}

// Fonction d'envoi d'emails de facture/devis
export const sendInvoiceEmail = async (email: string, data: InvoiceEmailData) => {
  try {
    if (!isValidEmail(email)) {
      throw new EmailServiceError(
        EMAIL_ERRORS.INVALID_EMAIL.message(email),
        EMAIL_ERRORS.INVALID_EMAIL.code
      );
    }

    const mailOptions = {
      from: config.smtp.auth.user,
      to: email,
      subject: data.subject,
      html: data.html,
      attachments: data.attachments
    };

    // Utiliser la queue pour tous les emails, avec ou sans attachments
    await queueEmail(mailOptions.to, mailOptions.subject, mailOptions.html, data.attachments);

    logger.info(`Email de facture/devis envoyé à ${email}`);

  } catch (error: any) {
    logger.error(`Erreur lors de l'envoi de l'email de facture/devis à ${email}:`, error);
    throw new EmailServiceError(
      EMAIL_ERRORS.SENDING_ERROR.message(error.message),
      EMAIL_ERRORS.SENDING_ERROR.code,
      { error: error.message }
    );
  }
};

// Fonction pour envoyer un email de changement de statut d'abonnement
export const sendSubscriptionStatusChangeEmail = async (email: string, data: SubscriptionStatusChangeEmailData) => {
  try {
    await queueEmail(email, getSubscriptionChangeSubject(data.changeType, data.newPlan), data.html);

    logger.info(`Email de changement d'abonnement envoyé à ${email}`, {
      type: 'subscription_status_change',
      changeType: data.changeType,
      newPlan: data.newPlan
    });
  } catch (error) {
    logger.error(`Erreur lors de l'envoi de l'email de changement d'abonnement à ${email}`, { error });
    throw error;
  }
};

// Fonction pour obtenir le sujet de l'email en fonction du type de changement
function getSubscriptionChangeSubject(changeType: 'upgrade' | 'downgrade' | 'renewal' | 'cancellation' | 'modification' | 'cancel_auto_renew' | 'reactivate_auto_renew' | 'reactivate_after_ban' | 'renewal_reminder', newPlan: string): string {
  switch (changeType) {
    case 'upgrade':
      return `Votre abonnement a été mis à niveau vers ${newPlan} 🚀`;
    case 'downgrade':
      return `Votre abonnement est passé au plan ${newPlan} ℹ️`;
    case 'renewal':
      return `Votre abonnement ${newPlan} a été renouvelé ✅`;
    case 'renewal_reminder':
      return `Rappel : renouvellement automatique de votre abonnement Premium dans 7 jours`;
    case 'cancellation':
      return `Votre abonnement a été annulé ❌`;
    case 'modification':
      return `Votre abonnement a été modifié ✅`;
    case 'cancel_auto_renew':
      return `Renouvellement automatique désactivé ℹ️`;
    case 'reactivate_auto_renew':
      return `Renouvellement automatique réactivé ✅`;
    case 'reactivate_after_ban':
      return `Votre abonnement Premium a été réactivé ✅`;
    default:
      return `Changement de statut de votre abonnement`;
  }
}

// Email de vérification pour l'abonnement à la newsletter
export const sendNewsletterVerificationEmail = async (email: string, data: { token: string }) => {
  try {
    // Vérification du rate limit
    // checkRateLimit(email);

    const verificationLink = `${process.env.FRONTEND_URL}/newsletter/verify?token=${data.token}`;

    const mailOptions = {
      from: `"Job Partiel" <${process.env.SMTP_FROM}>` || '<EMAIL>',
      to: email,
      subject: `🔔 Confirmez votre abonnement à la newsletter JobPartiel`,
      html: `
      <div style="font-family: 'Arial', sans-serif; width: 100%; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #FFF8F3 0%, #FFE4BA 100%); padding: 40px 0;">
        <div style="background-color: white; padding: 40px 20px; border-radius: 16px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
          <div style="text-align: center; margin-bottom: 30px;">
            <img src="${config.apiUrl}/api/storage-proxy/photo_profil/avatar/avatar-defaut-jobpartiel.jpg" alt="Job Partiel" style="max-width: 100%; height: auto;">
          </div>
          <h1 style="color: #FF7A35; text-align: center; margin-bottom: 20px; font-size: 24px;">Confirmez votre abonnement à notre newsletter</h1>
          <p style="color: #555; font-size: 16px; line-height: 1.6; margin-bottom: 25px;">
            Merci de vous être inscrit à la newsletter de JobPartiel ! Pour finaliser votre inscription et commencer à recevoir nos actualités, veuillez confirmer votre adresse email.
          </p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${verificationLink}" style="background-color: #FF7A35; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; font-weight: bold; display: inline-block; font-size: 16px;">Confirmer mon email</a>
          </div>
          <div style="background-color: #FFF8F3; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #FF7A35;">
            <p style="color: #555; font-size: 14px; line-height: 1.6; margin: 0;">
              Si le bouton ne fonctionne pas, copiez et collez ce lien dans votre navigateur :
              <br>
              <span style="color: #333; word-break: break-all; font-family: monospace; background-color: #f5f5f5; padding: 5px; display: block; margin-top: 5px;">${verificationLink}</span>
            </p>
          </div>
          <p style="color: #555; font-size: 14px; line-height: 1.6;">
            Si vous n'avez pas demandé à vous abonner à notre newsletter, vous pouvez ignorer cet email.
          </p>
          <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #eee; text-align: center; color: #888; font-size: 12px;">
            <p>© ${new Date().getFullYear()} JobPartiel.fr - Tous droits réservés</p>
            <p>
              <a href="${process.env.FRONTEND_URL}/politique-confidentialite" style="color: #FF7A35; text-decoration: none;">Politique de confidentialité</a> |
              <a href="${process.env.FRONTEND_URL}/newsletter/unsubscribe?email=${email}" style="color: #FF7A35; text-decoration: none;">Se désabonner</a>
            </p>
          </div>
        </div>
      </div>
      `
    };

    // Vérifier si un email similaire est déjà en file d'attente
    const emailKey = `newsletter:verification:${email}`;
    const existingEmail = await redis.get(emailKey);

    if (!existingEmail) {
      // Marquer cet email comme en cours d'envoi pour éviter les doublons
      await redis.setex(emailKey, 60, 'pending'); // Expire après 60 secondes

      await queueEmail(mailOptions.to, mailOptions.subject, mailOptions.html);
      logSecurity.info(LogEventType.EMAIL_SENT, 'Email de vérification newsletter envoyé avec succès', { to: email });
    } else {
      logSecurity.info(LogEventType.EMAIL_SENT, 'Email de vérification newsletter déjà en file d\'attente, envoi ignoré', { to: email });
    }
  } catch (error) {
    if (error instanceof EmailServiceError) {
      throw error;
    }

    const errorMessage = error instanceof Error ? error.message : 'Une erreur inconnue est survenue';
    throw new EmailServiceError(
      EMAIL_ERRORS.SMTP_ERROR.message(errorMessage),
      EMAIL_ERRORS.SMTP_ERROR.code,
      { originalError: errorMessage }
    );
  }
};

// Email de bienvenue après confirmation de l'abonnement à la newsletter
export const sendNewsletterWelcomeEmail = async (email: string) => {
  try {
    // Vérification du rate limit
    // checkRateLimit(email);

    const mailOptions = {
      from: `"Job Partiel" <${process.env.SMTP_FROM}>` || '<EMAIL>',
      to: email,
      subject: `🎉 Bienvenue dans la communauté JobPartiel !`,
      html: `
      <div style="font-family: 'Arial', sans-serif; width: 100%; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #FFF8F3 0%, #FFE4BA 100%); padding: 40px 0;">
        <div style="background-color: white; padding: 40px 20px; border-radius: 16px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
          <div style="text-align: center; margin-bottom: 30px;">
            <img src="${config.apiUrl}/api/storage-proxy/photo_profil/avatar/avatar-defaut-jobpartiel.jpg" alt="Job Partiel" style="max-width: 100%; height: auto;">
          </div>
          <h1 style="color: #FF7A35; text-align: center; margin-bottom: 20px; font-size: 24px;">Bienvenue dans notre communauté !</h1>
          <p style="color: #555; font-size: 16px; line-height: 1.6; margin-bottom: 25px;">
            Merci d'avoir confirmé votre abonnement à la newsletter de JobPartiel ! Vous recevrez désormais nos actualités, conseils et offres spéciales directement dans votre boîte mail.
          </p>
          <div style="background-color: #FFF8F3; padding: 20px; border-radius: 8px; margin: 30px 0;">
            <h2 style="color: #FF7A35; font-size: 18px; margin-bottom: 15px;">Ce que vous pouvez attendre de notre newsletter :</h2>
            <ul style="color: #555; font-size: 15px; line-height: 1.6; padding-left: 20px;">
              <li>Des conseils pratiques pour optimiser vos missions</li>
              <li>Des actualités sur les nouvelles fonctionnalités de la plateforme</li>
              <li>Des offres spéciales exclusives pour nos abonnés</li>
              <li>Des témoignages inspirants de notre communauté</li>
            </ul>
          </div>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.FRONTEND_URL}" style="background-color: #FF7A35; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; font-weight: bold; display: inline-block; font-size: 16px;">Visiter JobPartiel</a>
          </div>
          <div style="background-color: #FFF8F3; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #FF7A35;">
            <p style="color: #555; font-size: 14px; line-height: 1.6; margin: 0;">
              Si le bouton ne fonctionne pas, copiez et collez ce lien dans votre navigateur :
              <br>
              <span style="color: #333; word-break: break-all; font-family: monospace; background-color: #f5f5f5; padding: 5px; display: block; margin-top: 5px;">${process.env.FRONTEND_URL}</span>
            </p>
          </div>
          <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #eee; text-align: center; color: #888; font-size: 12px;">
            <p>© ${new Date().getFullYear()} JobPartiel.fr - Tous droits réservés</p>
            <p>
              <a href="${process.env.FRONTEND_URL}/politique-confidentialite" style="color: #FF7A35; text-decoration: none;">Politique de confidentialité</a> |
              <a href="${process.env.FRONTEND_URL}/newsletter/unsubscribe?email=${email}" style="color: #FF7A35; text-decoration: none;">Se désabonner</a>
            </p>
          </div>
        </div>
      </div>
      `
    };

    // Vérifier si un email similaire est déjà en file d'attente
    const emailKey = `newsletter:welcome:${email}`;
    const existingEmail = await redis.get(emailKey);

    if (!existingEmail) {
      // Marquer cet email comme en cours d'envoi pour éviter les doublons
      await redis.setex(emailKey, 60, 'pending'); // Expire après 60 secondes

      await queueEmail(mailOptions.to, mailOptions.subject, mailOptions.html);
      logSecurity.info(LogEventType.EMAIL_SENT, 'Email de bienvenue newsletter envoyé avec succès', { to: email });
    } else {
      logSecurity.info(LogEventType.EMAIL_SENT, 'Email de bienvenue newsletter déjà en file d\'attente, envoi ignoré', { to: email });
    }
  } catch (error) {
    if (error instanceof EmailServiceError) {
      throw error;
    }

    const errorMessage = error instanceof Error ? error.message : 'Une erreur inconnue est survenue';
    throw new EmailServiceError(
      EMAIL_ERRORS.SMTP_ERROR.message(errorMessage),
      EMAIL_ERRORS.SMTP_ERROR.code,
      { originalError: errorMessage }
    );
  }
};

// Email de confirmation de désabonnement à la newsletter
export const sendNewsletterUnsubscribeEmail = async (email: string) => {
  try {
    // Vérification du rate limit
    // checkRateLimit(email);

    const mailOptions = {
      from: `"Job Partiel" <${process.env.SMTP_FROM}>` || '<EMAIL>',
      to: email,
      subject: `👋 Confirmation de désabonnement à la newsletter JobPartiel`,
      html: `
      <div style="font-family: 'Arial', sans-serif; width: 100%; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #FFF8F3 0%, #FFE4BA 100%); padding: 40px 0;">
        <div style="background-color: white; padding: 40px 20px; border-radius: 16px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
          <div style="text-align: center; margin-bottom: 30px;">
            <img src="${config.apiUrl}/api/storage-proxy/photo_profil/avatar/avatar-defaut-jobpartiel.jpg" alt="Job Partiel" style="max-width: 100%; height: auto;">
          </div>
          <h1 style="color: #FF7A35; text-align: center; margin-bottom: 20px; font-size: 24px;">Désabonnement confirmé</h1>
          <p style="color: #555; font-size: 16px; line-height: 1.6; margin-bottom: 25px;">
            Nous confirmons que vous avez été désabonné de la newsletter de JobPartiel. Vous ne recevrez plus d'emails concernant nos actualités et offres spéciales.
          </p>
          <p style="color: #555; font-size: 16px; line-height: 1.6; margin-bottom: 25px;">
            Si ce désabonnement a été effectué par erreur, vous pouvez vous réabonner à tout moment en visitant notre site.
          </p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.FRONTEND_URL}" style="background-color: #FF7A35; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; font-weight: bold; display: inline-block; font-size: 16px;">Visiter JobPartiel</a>
          </div>
          <div style="background-color: #FFF8F3; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #FF7A35;">
            <p style="color: #555; font-size: 14px; line-height: 1.6; margin: 0;">
              Si le bouton ne fonctionne pas, copiez et collez ce lien dans votre navigateur :
              <br>
              <span style="color: #333; word-break: break-all; font-family: monospace; background-color: #f5f5f5; padding: 5px; display: block; margin-top: 5px;">${process.env.FRONTEND_URL}</span>
            </p>
          </div>
          <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #eee; text-align: center; color: #888; font-size: 12px;">
            <p>© ${new Date().getFullYear()} JobPartiel.fr - Tous droits réservés</p>
            <p>
              <a href="${process.env.FRONTEND_URL}/politique-confidentialite" style="color: #FF7A35; text-decoration: none;">Politique de confidentialité</a>
            </p>
          </div>
        </div>
      </div>
      `
    };

    // Vérifier si un email similaire est déjà en file d'attente
    const emailKey = `newsletter:unsubscribe:${email}`;
    const existingEmail = await redis.get(emailKey);

    if (!existingEmail) {
      // Marquer cet email comme en cours d'envoi pour éviter les doublons
      await redis.setex(emailKey, 60, 'pending'); // Expire après 60 secondes

      await queueEmail(mailOptions.to, mailOptions.subject, mailOptions.html);
      logSecurity.info(LogEventType.EMAIL_SENT, 'Email de confirmation de désabonnement newsletter envoyé avec succès', { to: email });
    } else {
      logSecurity.info(LogEventType.EMAIL_SENT, 'Email de confirmation de désabonnement newsletter déjà en file d\'attente, envoi ignoré', { to: email });
    }
  } catch (error) {
    if (error instanceof EmailServiceError) {
      throw error;
    }

    const errorMessage = error instanceof Error ? error.message : 'Une erreur inconnue est survenue';
    throw new EmailServiceError(
      EMAIL_ERRORS.SMTP_ERROR.message(errorMessage),
      EMAIL_ERRORS.SMTP_ERROR.code,
      { originalError: errorMessage }
    );
  }
};

// Email de relance pour la vérification de la newsletter
export const sendNewsletterReminderEmail = async (email: string, data: { token: string, reminderCount: number }) => {
  try {
    // Vérification du rate limit
    // checkRateLimit(email);

    const verificationLink = `${process.env.FRONTEND_URL}/newsletter/verify?token=${data.token}`;

    // Adapter le sujet et le contenu en fonction du nombre de relances
    let subject = '';
    let urgencyText = '';

    if (data.reminderCount === 1) {
      subject = '🔔 Rappel: Confirmez votre abonnement à la newsletter JobPartiel';
      urgencyText = 'Nous avons remarqué que vous n\'avez pas encore confirmé votre abonnement à notre newsletter.';
    } else if (data.reminderCount === 2) {
      subject = '⚠️ Dernier rappel: Confirmez votre abonnement à la newsletter JobPartiel';
      urgencyText = 'Ceci est votre dernier rappel pour confirmer votre abonnement à notre newsletter. Sans confirmation, votre inscription sera automatiquement supprimée.';
    } else {
      subject = '🚨 Action requise: Confirmez votre abonnement à la newsletter JobPartiel';
      urgencyText = 'Votre inscription à notre newsletter sera supprimée dans 24 heures si vous ne confirmez pas votre email.';
    }

    const mailOptions = {
      from: `"Job Partiel" <${process.env.SMTP_FROM}>` || '<EMAIL>',
      to: email,
      subject: subject,
      html: `
      <div style="font-family: 'Arial', sans-serif; width: 100%; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #FFF8F3 0%, #FFE4BA 100%); padding: 40px 0;">
        <div style="background-color: white; padding: 40px 20px; border-radius: 16px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
          <div style="text-align: center; margin-bottom: 30px;">
            <img src="${config.apiUrl}/api/storage-proxy/photo_profil/avatar/avatar-defaut-jobpartiel.jpg" alt="Job Partiel" style="max-width: 100%; height: auto;">
          </div>
          <h1 style="color: #FF7A35; text-align: center; margin-bottom: 20px; font-size: 24px;">Rappel: Confirmez votre abonnement</h1>
          <p style="color: #555; font-size: 16px; line-height: 1.6; margin-bottom: 25px;">
            ${urgencyText}
          </p>
          <p style="color: #555; font-size: 16px; line-height: 1.6; margin-bottom: 25px;">
            Pour finaliser votre inscription et commencer à recevoir nos actualités, veuillez confirmer votre adresse email en cliquant sur le bouton ci-dessous.
          </p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${verificationLink}" style="background-color: #FF7A35; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; font-weight: bold; display: inline-block; font-size: 16px;">Confirmer mon email</a>
          </div>
          <div style="background-color: #FFF8F3; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #FF7A35;">
            <p style="color: #555; font-size: 14px; line-height: 1.6; margin: 0;">
              Si le bouton ne fonctionne pas, copiez et collez ce lien dans votre navigateur :
              <br>
              <a href="${verificationLink}" style="color: #FF7A35; word-break: break-all;">${verificationLink}</a>
            </p>
          </div>
          <p style="color: #555; font-size: 14px; line-height: 1.6;">
            Si vous n'avez pas demandé à vous abonner à notre newsletter, vous pouvez ignorer cet email.
          </p>
          <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #eee; text-align: center; color: #888; font-size: 12px;">
            <p>© ${new Date().getFullYear()} JobPartiel.fr - Tous droits réservés</p>
            <p>
              <a href="${process.env.FRONTEND_URL}/politique-confidentialite" style="color: #FF7A35; text-decoration: none;">Politique de confidentialité</a> |
              <a href="${process.env.FRONTEND_URL}/newsletter/unsubscribe?email=${email}" style="color: #FF7A35; text-decoration: none;">Se désabonner</a>
            </p>
          </div>
        </div>
      </div>
      `
    };

    // Vérifier si un email similaire est déjà en file d'attente
    const emailKey = `newsletter:reminder:${email}:${data.reminderCount}`;
    const existingEmail = await redis.get(emailKey);

    if (!existingEmail) {
      // Marquer cet email comme en cours d'envoi pour éviter les doublons
      await redis.setex(emailKey, 60 * 60 * 24, 'pending'); // Expire après 24 heures

      await queueEmail(mailOptions.to, mailOptions.subject, mailOptions.html);
      logSecurity.info(LogEventType.EMAIL_SENT, `Email de relance newsletter #${data.reminderCount} envoyé avec succès`, { to: email });
    } else {
      logSecurity.info(LogEventType.EMAIL_SENT, `Email de relance newsletter #${data.reminderCount} déjà en file d'attente, envoi ignoré`, { to: email });
    }
  } catch (error) {
    if (error instanceof EmailServiceError) {
      throw error;
    }

    const errorMessage = error instanceof Error ? error.message : 'Une erreur inconnue est survenue';
    throw new EmailServiceError(
      EMAIL_ERRORS.SMTP_ERROR.message(errorMessage),
      EMAIL_ERRORS.SMTP_ERROR.code,
      { originalError: errorMessage }
    );
  }
};

// Email de partage de profil
export const sendProfileShareEmail = async (email: string, data: {
  profileData: {
    firstName: string;
    lastName: string;
    slug: string;
    bio?: string;
  };
  personalMessage: string;
  senderName: string;
  senderProfileUrl?: string;
}) => {
  try {
    if (!isValidEmail(email)) {
      throw new EmailServiceError(
        EMAIL_ERRORS.INVALID_EMAIL.message(email),
        EMAIL_ERRORS.INVALID_EMAIL.code
      );
    }

    // Vérification du rate limit
    checkRateLimit(email);

    const profileUrl = `${process.env.FRONTEND_URL}/profil/${data.profileData.slug}`;
    const bioText = data.profileData.bio ? data.profileData.bio.replace(/<[^>]*>/g, '').substring(0, 200) : '';

    const mailOptions = {
      from: `"Job Partiel" <${process.env.SMTP_FROM}>` || '<EMAIL>',
      to: email,
      subject: `🔗 ${data.senderName} vous recommande un profil sur JobPartiel`,
      html: `
      <div style="font-family: 'Arial', sans-serif; width: 100%; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #FFF8F3 0%, #FFE4BA 100%); padding: 40px 0;">
        <div style="background-color: white; padding: 40px 20px; border-radius: 16px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
          <div style="text-align: center; margin-bottom: 30px;">
            <img src="${config.apiUrl}/api/storage-proxy/photo_profil/avatar/avatar-defaut-jobpartiel.jpg" alt="Job Partiel" style="max-width: 100%; height: auto;">
          </div>

          <h1 style="color: #FF6B2C; text-align: center; margin-bottom: 20px; font-size: 24px;">
            Profil recommandé sur JobPartiel
          </h1>

          <div style="background-color: #FFF8F3; padding: 20px; border-radius: 12px; margin: 25px 0; border-left: 4px solid #FF6B2C;">
            <p style="color: #555; font-size: 16px; line-height: 1.6; margin: 0;">
              ${data.senderProfileUrl ?
                `<strong><a href="${data.senderProfileUrl}" style="color: #FF6B2C; text-decoration: none;">${data.senderName}</a></strong>` :
                `<strong>${data.senderName}</strong>`
              } vous recommande le profil de <strong>${data.profileData.firstName} ${data.profileData.lastName}</strong> sur JobPartiel.fr.
            </p>
          </div>

          ${data.personalMessage ? `
          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 12px; margin: 25px 0; border-left: 4px solid #6c757d;">
            <h3 style="color: #495057; font-size: 16px; margin: 0 0 10px 0;">Message personnel :</h3>
            <p style="color: #555; font-size: 15px; line-height: 1.6; margin: 0; font-style: italic;">
              "${data.personalMessage}"
            </p>
          </div>
          ` : ''}

          <div style="background-color: #fff; border: 2px solid #FF6B2C; border-radius: 12px; padding: 25px; margin: 25px 0;">
            <h2 style="color: #FF6B2C; font-size: 20px; margin: 0 0 15px 0; text-align: center;">
              ${data.profileData.firstName} ${data.profileData.lastName}
            </h2>

            ${bioText ? `
            <p style="color: #555; font-size: 15px; line-height: 1.6; margin: 15px 0; text-align: center;">
              ${bioText}${data.profileData.bio && data.profileData.bio.length > 200 ? '...' : ''}
            </p>
            ` : ''}

            <div style="text-align: center; margin: 25px 0;">
              <a href="${profileUrl}" style="background-color: #FF6B2C; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; display: inline-block; font-size: 16px; transition: background-color 0.3s;">
                Voir le profil complet
              </a>
            </div>
          </div>

          <div style="background-color: #e8f4fd; padding: 20px; border-radius: 12px; margin: 25px 0;">
            <h3 style="color: #0066cc; font-size: 18px; margin: 0 0 15px 0;">
              🏡 Qu'est-ce que JobPartiel ?
            </h3>
            <p style="color: #555; font-size: 15px; line-height: 1.6; margin: 0;">
              JobPartiel.fr est une plateforme de Jobbing qui met en relation des particuliers et des professionnels vérifiés pour des services de <strong>jardinage</strong>, <strong>bricolage</strong> et <strong>garde d'animaux</strong>. Tous nos jobbeurs sont vérifiés et notés par la communauté.
            </p>
          </div>

          ${data.senderProfileUrl ? `
          <div style="background-color: #f0f8ff; padding: 20px; border-radius: 12px; margin: 25px 0; text-align: center;">
            <p style="color: #555; font-size: 15px; line-height: 1.6; margin: 0 0 15px 0;">
              Vous souhaitez en savoir plus sur la personne qui vous recommande ce profil ?
            </p>
            <a href="${data.senderProfileUrl}" style="background-color: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 8px; font-weight: bold; display: inline-block; font-size: 14px;">
              Voir le profil de ${data.senderName.split(' ')[0]} ${data.senderName.split(' ')[1] ? data.senderName.split(' ')[1].charAt(0) + '.' : ''}
            </a>
          </div>
          ` : ''}

          <div style="background-color: #FFF8F3; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #FF6B2C;">
            <p style="color: #555; font-size: 14px; line-height: 1.6; margin: 0;">
              Si le bouton ne fonctionne pas, copiez et collez ce lien dans votre navigateur :
              <br>
              <a href="${profileUrl}" style="color: #FF6B2C; word-break: break-all;">${profileUrl}</a>
            </p>
          </div>

          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.FRONTEND_URL}" style="background-color: #FF7A35; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; font-weight: bold; display: inline-block; font-size: 16px;">
              Découvrir JobPartiel
            </a>
          </div>

          <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #eee; text-align: center; color: #888; font-size: 12px;">
            <p>© ${new Date().getFullYear()} JobPartiel.fr - Tous droits réservés</p>
            <p>
              <a href="${process.env.FRONTEND_URL}/politique-confidentialite" style="color: #FF6B2C; text-decoration: none;">Politique de confidentialité</a> |
              <a href="${process.env.FRONTEND_URL}/cgu" style="color: #FF6B2C; text-decoration: none;">Conditions d'utilisation</a>
            </p>
          </div>
        </div>
      </div>
      `
    };

    await queueEmail(mailOptions.to, mailOptions.subject, mailOptions.html);
    logSecurity.info(LogEventType.EMAIL_SENT, 'Email de partage de profil envoyé avec succès', {
      to: email,
      profileShared: `${data.profileData.firstName} ${data.profileData.lastName}`,
      sharedBy: data.senderName
    });
  } catch (error) {
    if (error instanceof EmailServiceError) {
      throw error;
    }

    const errorMessage = error instanceof Error ? error.message : 'Une erreur inconnue est survenue';
    throw new EmailServiceError(
      EMAIL_ERRORS.SMTP_ERROR.message(errorMessage),
      EMAIL_ERRORS.SMTP_ERROR.code,
      { originalError: errorMessage }
    );
  }
};

// Email de confirmation de suppression de compte
export const sendAccountDeletionConfirmationEmail = async (email: string, token: string) => {
  try {
    if (!email || !isValidEmail(email)) {
      throw new EmailServiceError(
        EMAIL_ERRORS.INVALID_EMAIL.message(email || 'undefined'),
        EMAIL_ERRORS.INVALID_EMAIL.code
      );
    }

    // Vérification du rate limit
    checkRateLimit(email);

    const confirmationLink = `${process.env.FRONTEND_URL}/account/delete/confirm?token=${token}`;

    const mailOptions = {
      from: `"Job Partiel" <${process.env.SMTP_FROM}>` || '<EMAIL>',
      to: email,
      subject: `${EMAIL_ICONS.accountDeletion} ${EMAIL_SUBJECTS.accountDeletion}`,
      html: `
      <div style="font-family: 'Arial', sans-serif; width: 100%; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #FFF8F3 0%, #FFE4BA 100%); padding: 40px 0;">
        <div style="background-color: white; padding: 40px 20px; border-radius: 16px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
          <div style="text-align: center; margin-bottom: 30px;">
            <img src="${config.apiUrl}/api/storage-proxy/photo_profil/avatar/avatar-defaut-jobpartiel.jpg" alt="Job Partiel" style="max-width: 100%; height: auto;">
          </div>

          <h1 style="color: #dc3545; text-align: center; margin-bottom: 20px; font-size: 24px;">
            🗑️ Confirmation de suppression de compte
          </h1>

          <div style="background-color: #f8d7da; padding: 20px; border-radius: 12px; margin: 25px 0; border-left: 4px solid #dc3545;">
            <p style="color: #721c24; font-size: 16px; line-height: 1.6; margin: 0; font-weight: bold;">
              ⚠️ Attention : Cette action est irréversible
            </p>
          </div>

          <p style="color: #555; font-size: 16px; line-height: 1.6; margin-bottom: 25px;">
            Vous avez demandé la suppression de votre compte JobPartiel. Pour des raisons de sécurité, nous devons confirmer cette demande.
          </p>

          <div style="background-color: #fff3cd; padding: 20px; border-radius: 12px; margin: 25px 0; border-left: 4px solid #ffc107;">
            <h3 style="color: #856404; font-size: 16px; margin: 0 0 10px 0;">Ce qui va se passer :</h3>
            <ul style="color: #856404; font-size: 14px; line-height: 1.6; margin: 0; padding-left: 20px;">
              <li>Vos données personnelles seront anonymisées</li>
              <li>Votre profil ne sera plus visible publiquement</li>
              <li>Vous ne pourrez plus vous connecter à votre compte</li>
              <li>Les données légalement requises seront conservées de manière anonyme</li>
            </ul>
          </div>

          <p style="color: #555; font-size: 16px; line-height: 1.6; margin-bottom: 25px;">
            Si vous êtes certain de vouloir supprimer votre compte, cliquez sur le bouton ci-dessous. Vous devrez ensuite vous re-authentifier pour confirmer votre identité.
          </p>

          <div style="text-align: center; margin: 30px 0;">
            <a href="${confirmationLink}" style="background-color: #dc3545; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; display: inline-block; font-size: 16px;">
              Confirmer la suppression
            </a>
          </div>

          <div style="background-color: #d1ecf1; padding: 20px; border-radius: 12px; margin: 25px 0; border-left: 4px solid #17a2b8;">
            <h3 style="color: #0c5460; font-size: 16px; margin: 0 0 10px 0;">Vous avez changé d'avis ?</h3>
            <p style="color: #0c5460; font-size: 14px; line-height: 1.6; margin: 0;">
              Si vous ne souhaitez plus supprimer votre compte, ignorez simplement cet email. Votre compte restera actif et aucune modification ne sera apportée.
            </p>
          </div>

          <div style="background-color: #FFF8F3; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #FF6B2C;">
            <p style="color: #555; font-size: 14px; line-height: 1.6; margin: 0;">
              <strong>Important :</strong> Ce lien expire dans 24 heures pour votre sécurité.
              <br><br>
              Si le bouton ne fonctionne pas, copiez et collez ce lien dans votre navigateur :
              <br>
              <a href="${confirmationLink}" style="color: #FF6B2C; word-break: break-all;">${confirmationLink}</a>
            </p>
          </div>

          <p style="color: #555; font-size: 14px; line-height: 1.6;">
            Si vous n'avez pas demandé la suppression de votre compte, veuillez ignorer cet email et contactez immédiatement notre support.
          </p>

          <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #eee; text-align: center; color: #888; font-size: 12px;">
            <p>© ${new Date().getFullYear()} JobPartiel.fr - Tous droits réservés</p>
            <p>
              <a href="${process.env.FRONTEND_URL}/support" style="color: #FF6B2C; text-decoration: none;">Contacter le support</a> |
              <a href="${process.env.FRONTEND_URL}/politique-confidentialite" style="color: #FF6B2C; text-decoration: none;">Politique de confidentialité</a>
            </p>
          </div>
        </div>
      </div>
      `
    };

    await queueEmail(mailOptions.to, mailOptions.subject, mailOptions.html);
    logSecurity.info(LogEventType.EMAIL_SENT, 'Email de confirmation de suppression de compte envoyé avec succès', { to: email });
  } catch (error) {
    if (error instanceof EmailServiceError) {
      throw error;
    }

    const errorMessage = error instanceof Error ? error.message : 'Une erreur inconnue est survenue';
    throw new EmailServiceError(
      EMAIL_ERRORS.SMTP_ERROR.message(errorMessage),
      EMAIL_ERRORS.SMTP_ERROR.code,
      { originalError: errorMessage }
    );
  }
};

// Email de confirmation après suppression de compte
export const sendAccountDeletionCompletedEmail = async (email: string) => {
  try {
    if (!email || !isValidEmail(email)) {
      throw new EmailServiceError(
        EMAIL_ERRORS.INVALID_EMAIL.message(email || 'undefined'),
        EMAIL_ERRORS.INVALID_EMAIL.code
      );
    }

    const subject = 'Confirmation de suppression de votre compte JobPartiel';

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Compte supprimé - JobPartiel</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #FF6B2C 0%, #FF7A35 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
          .content { background: #fff; padding: 30px; border: 1px solid #ddd; border-top: none; }
          .footer { background: #f8f9fa; padding: 20px; text-align: center; border-radius: 0 0 10px 10px; font-size: 12px; color: #666; }
          .success-icon { font-size: 48px; margin-bottom: 20px; }
          .info-box { background: #e8f5e8; border: 1px solid #4caf50; border-radius: 5px; padding: 15px; margin: 20px 0; }
          .warning-box { background: #fff3cd; border: 1px solid #ffc107; border-radius: 5px; padding: 15px; margin: 20px 0; }
          ul { padding-left: 20px; }
          li { margin-bottom: 8px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <div class="success-icon">✅</div>
            <h1 style="margin: 0; font-size: 24px;">Suppression confirmée</h1>
            <p style="margin: 10px 0 0 0; opacity: 0.9;">Votre compte JobPartiel a été supprimé avec succès</p>
          </div>

          <div class="content">
            <p>Bonjour,</p>

            <p>Nous vous confirmons que votre compte JobPartiel a été <strong>définitivement supprimé</strong> conformément à votre demande et au Règlement Général sur la Protection des Données (RGPD).</p>

            <div class="info-box">
              <h3 style="margin-top: 0; color: #4caf50;">✓ Données supprimées</h3>
              <ul>
                <li>Toutes vos données personnelles ont été anonymisées</li>
                <li>Vos photos et documents ont été supprimés</li>
                <li>Votre profil n'est plus visible publiquement</li>
                <li>Vos sessions ont été invalidées sur tous les appareils</li>
              </ul>
            </div>

            <div class="warning-box">
              <h3 style="margin-top: 0; color: #856404;">ℹ️ Données conservées (anonymisées)</h3>
              <p style="margin-bottom: 10px;">Conformément aux obligations légales, certaines données ont été conservées de manière <strong>anonyme</strong> :</p>
              <ul>
                <li>Historique des transactions (obligations comptables - 7 ans)</li>
                <li>Avis et évaluations (anonymisés pour la communauté)</li>
                <li>Données statistiques agrégées (sans identification possible)</li>
              </ul>
              <p style="margin-bottom: 0; font-size: 14px; color: #856404;"><em>Ces données ne permettent plus de vous identifier.</em></p>
            </div>

            <h3>Que se passe-t-il maintenant ?</h3>
            <ul>
              <li>Vous ne pouvez plus vous connecter à votre ancien compte</li>
              <li>Vous ne recevrez plus d'emails de notre part</li>
              <li>Si vous souhaitez revenir, vous pourrez créer un nouveau compte</li>
            </ul>

            <p><strong>Merci d'avoir utilisé JobPartiel.</strong> Nous espérons que notre service vous a été utile.</p>

            <p style="margin-top: 20px;">Veuillez noter qu'un délai de quelques heures peut être nécessaire pour que nos serveurs traitent l'ensemble des informations de suppression.</p>

            <p>Si vous avez des questions concernant cette suppression, vous pouvez nous contacter à <a href="mailto:<EMAIL>"><EMAIL></a>.</p>

            <p>Cordialement,<br>L'équipe JobPartiel</p>
          </div>

          <div class="footer">
            <p>Cet email confirme la suppression définitive de votre compte JobPartiel.<br>
            Conformément au RGPD, vos données personnelles ont été anonymisées.</p>
            <p>JobPartiel - Plateforme de services entre particuliers</p>
          </div>
        </div>
      </body>
      </html>
    `;

    await queueEmail(email, subject, html);
    logger.info('Email de confirmation de suppression envoyé', { email });

  } catch (error) {
    logger.error('Erreur lors de l\'envoi de l\'email de confirmation de suppression', { email, error });
    throw error;
  }
};

// Exporter le service d'email
export const emailService = {
  sendVerificationEmail,
  sendPasswordResetEmail,
  sendWelcomeEmail,
  sendPasswordExpirationWarningEmail,
  sendPasswordChangedEmail,
  sendLoginAttemptsWarningEmail,
  sendLoginNotificationEmail,
  sendSuspensionEmail,
  sendPhotosDeletionEmail,
  sendNewMissionEmail,
  sendMissionAutoClosureEmail,
  sendNewProposalEmail,
  sendCounterOfferEmail,
  sendProposalAcceptedEmail,
  sendProposalRejectedEmail,
  sendJobbeurCounterOfferEmail,
  sendContactInfoEmail,
  sendMissionCancelledEmail,
  sendOfferExpirationEmail,
  sendOfferExpirationReminderEmail,
  sendReferralWelcomeEmail,
  sendReferrerNotificationEmail,
  sendReferralRewardEmail,
  sendNewBugReportEmail,
  sendBugReportCommentEmail,
  sendBugReportStatusChangeEmail,
  sendEmailChangeNotification,
  sendEmailChangeConfirmation,
  sendNewSupportTicketEmail,
  sendSupportTicketCommentEmail,
  sendSupportTicketStatusChangeEmail,
  sendSupportTicketAssignedEmail,
  sendJobiExchangeEmail,
  sendNewMessageEmail,
  sendNewConversationEmail,
  sendReviewReceivedEmail,
  sendReviewPostedEmail,
  sendReviewReplyEmail,
  sendReviewDeletedEmail,
  sendPaymentReceivedEmail,
  sendPaymentConfirmedEmail,
  sendNewBadgesEmail,
  sendInvoiceEmail,
  sendSubscriptionStatusChangeEmail,
  sendModerationActionEmail,
  sendEntrepriseVerificationStatus,
  sendNewsletterVerificationEmail,
  sendNewsletterWelcomeEmail,
  sendNewsletterUnsubscribeEmail,
  sendNewsletterReminderEmail,
  sendAiCreditsEmail,
  sendProfileShareEmail,
  sendAccountDeletionConfirmationEmail,
  sendAccountDeletionCompletedEmail,
};