import React, { useState, useEffect, useCallback } from 'react';
import AiConsentModal from './AiConsentModal';
import useAiConsent from '../../hooks/useAiConsent';
import { notify } from '../Notification';
import logger from '@/utils/logger';

interface AiConsentProviderProps {
  children: React.ReactNode;
}

/**
 * Composant qui gère le consentement IA au niveau global de l'application
 * Écoute les événements pour ouvrir la modale de consentement
 */
const AiConsentProvider: React.FC<AiConsentProviderProps> = ({ children }) => {
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const { checkConsent } = useAiConsent();

  // Gestionnaire pour ouvrir la modale de consentement
  const handleOpenModal = useCallback(() => {
    console.log('🔓 AiConsentProvider: Ouverture de la modal de consentement');
    setIsModalOpen(true);
  }, []);

  // Gestionnaire pour fermer la modale de consentement
  const handleCloseModal = useCallback(() => {
    setIsModalOpen(false);
  }, []);

  // Gestionnaire pour accepter le consentement
  const handleAcceptConsent = useCallback(async () => {
    try {
      // La modale AiConsentModal gère déjà l'appel à setConsent
      setIsModalOpen(false);
      await checkConsent();
    } catch (error) {
      logger.error('Erreur lors de l\'acceptation du consentement IA:', error);
      notify('Une erreur est survenue lors de l\'enregistrement de votre consentement', 'error');
    }
  }, [checkConsent]);

  // Écouter l'événement personnalisé pour ouvrir la modale de consentement
  useEffect(() => {
    const handleOpenConsentModal = () => {
      console.log('🎯 Événement open-ai-consent-modal reçu dans AiConsentProvider');
      handleOpenModal();
    };

    console.log('📡 AiConsentProvider: Ajout de l\'écouteur d\'événement open-ai-consent-modal');
    window.addEventListener('open-ai-consent-modal', handleOpenConsentModal);

    return () => {
      console.log('🔌 AiConsentProvider: Suppression de l\'écouteur d\'événement open-ai-consent-modal');
      window.removeEventListener('open-ai-consent-modal', handleOpenConsentModal);
    };
  }, [handleOpenModal]);

  return (
    <>
      {children}
      <AiConsentModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        onAccept={handleAcceptConsent}
      />
    </>
  );
};

export default AiConsentProvider;
