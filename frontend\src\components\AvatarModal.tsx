import React, { useEffect, useState, useRef, useCallback } from 'react';
import ModalPortal from './ModalPortal';
import { notify } from './Notification';
import { X, Upload, Grid, Sparkles } from 'lucide-react';
import DOMPurify from 'dompurify';
import { API_CONFIG } from '../config/api';
import logger from '../utils/logger';
import AvatarPreviewModal from './AvatarPreviewModal';
import { getCommonHeaders } from '../utils/headers';
import { fetchCsrfToken } from '../services/csrf';
import useImageModeration from '../hooks/useImageModeration';
import ImageModerationStatus from './ImageModerationStatus';
import RejectedImageMessage from './RejectedImageMessage';
import AiImageGenerationButton from './ai/AiImageGenerationButton';
import { compressBannerPhoto, compressProfilPhoto } from '../utils/imageCompressor';
import useAiConsent from '../hooks/useAiConsent';

interface AvatarModalProps {
  isOpen: boolean;
  onClose: () => void;
  onUploadPhoto: (file: File) => void;
  onSelectAvatar?: (avatarUrl: string) => void;
  firstName?: string;
  lastName?: string;
  title?: string;
  uploadLabel?: string;
  isBannerMode?: boolean;
  onSelectBannerPosition?: (position: 'top' | 'center' | 'bottom') => void;
}

const ITEMS_PER_PAGE = 50;

const AvatarModal: React.FC<AvatarModalProps> = ({
  isOpen,
  onClose,
  onUploadPhoto,
  onSelectAvatar,
  firstName,
  lastName,
  title = "Personnaliser votre photo",
  uploadLabel = "Choisir une photo",
  isBannerMode = false,
  onSelectBannerPosition
}) => {
  const [avatars, setAvatars] = useState<string[]>([]);
  const [displayedAvatars, setDisplayedAvatars] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);
  const containerRef = useRef<HTMLDivElement>(null);
  const [selectedAvatar, setSelectedAvatar] = useState<string | null>(null);
  const [showPreview, setShowPreview] = useState(false);
  const { hasConsent, isLoading: consentLoading } = useAiConsent();
  const [activeTab, setActiveTab] = useState<'upload' | 'avatar' | 'ai'>(isBannerMode ? 'upload' : 'upload');
  const [isModerationModalOpen, setIsModerationModalOpen] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [moderationResult, setModerationResult] = useState<{ isSafe: boolean; description?: string; improvementSuggestions?: string } | null>(null);
  const { moderateImage, isLoading: isModerationLoading } = useImageModeration();
  const [selectedBannerPosition, setSelectedBannerPosition] = useState<'top' | 'center' | 'bottom'>('center');

  // Vérification du profil utilisateur pour l'onglet IA
  const hasFullName = !!firstName && !!lastName && firstName !== 'Prénom' && lastName !== 'Nom';

  useEffect(() => {
    const fetchAvatars = async () => {
      try {
        await fetchCsrfToken();
        const headers = await getCommonHeaders();
        const response = await fetch(`${API_CONFIG.baseURL}/api/users/avatars`, {
          method: 'GET',
          headers,
          credentials: 'include'
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Erreur lors du chargement des avatars');
        }

        const data = await response.json();
        logger.info(`Nombre total d'avatars reçus: ${data.avatars.length}`);
        setAvatars(data.avatars);
        setDisplayedAvatars(data.avatars.slice(0, ITEMS_PER_PAGE));
        setPage(1);
      } catch (error) {
        logger.error('Erreur détaillée:', error);
        notify(error instanceof Error ? error.message : 'Erreur lors du chargement des avatars', 'error');
      } finally {
        setLoading(false);
      }
    };

    if (isOpen) {
      setLoading(true);
      fetchAvatars();
    } else {
      // Réinitialiser l'état quand la modale se ferme
      setAvatars([]);
      setDisplayedAvatars([]);
      setPage(1);
      setSelectedFile(null);
      setPreviewUrl(null);
      setModerationResult(null);
      setActiveTab('upload');
      setSelectedBannerPosition('center');
    }
  }, [isOpen]);

  const loadMore = useCallback(() => {
    if (loading) return;

    const nextPage = page + 1;
    const start = (nextPage - 1) * ITEMS_PER_PAGE;
    const end = nextPage * ITEMS_PER_PAGE;

    if (start < avatars.length) {
      setDisplayedAvatars(prev => [...prev, ...avatars.slice(start, end)]);
      setPage(nextPage);
      logger.info(`Chargement de plus d'avatars: ${start} à ${end}`);
    }
  }, [page, avatars, loading]);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const handleScroll = () => {
      const { scrollTop, scrollHeight, clientHeight } = container;
      // Charger plus d'avatars quand on est à 70% du scroll pour une expérience plus fluide
      if (scrollHeight - scrollTop <= clientHeight * 1.5) {
        const maxPages = Math.ceil(avatars.length / ITEMS_PER_PAGE);
        if (page < maxPages && !loading) {
          loadMore();
        }
      }
    };

    container.addEventListener('scroll', handleScroll);
    return () => container.removeEventListener('scroll', handleScroll);
  }, [loadMore, page, avatars.length, loading]);

  const handleAvatarClick = (avatar: string) => {
    setSelectedAvatar(avatar);
    setShowPreview(true);
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Vérification du type et de la taille du fichier
    const validImageTypes = ['image/jpeg', 'image/png', 'image/webp'];
    if (!validImageTypes.includes(file.type)) {
      notify('Format de fichier non supporté. Utilisez JPG, PNG ou WEBP.', 'error');
      return;
    }

    const maxSize = 5 * 1024 * 1024; // 5MB
    const fileSizeInMB = (file.size / 1024 / 1024).toFixed(2);
    if (file.size > maxSize) {
      notify('La taille du fichier ne doit pas dépasser 5 MB. Votre fichier fait ' + fileSizeInMB + ' MB.', 'error');
      return;
    }

    // Créer une URL pour la prévisualisation
    const objectUrl = URL.createObjectURL(file);
    setPreviewUrl(objectUrl);
    setSelectedFile(file);

    // Ouvrir la modal de modération
    setIsModerationModalOpen(true);

    try {
      // Générer un ID temporaire pour l'image
      const tempImageId = `image-temp-${Date.now()}`;

      // Modérer l'image avec l'ID temporaire
      const result = await moderateImage(file, 'profile_picture', tempImageId);

      // Stocker le résultat de la modération
      setModerationResult(result);

      if (result.isSafe) {
        // L'image est sûre, on peut l'envoyer
        // Fermer la modale de modération après l'analyse
        setIsModerationModalOpen(false);
        notify('Image validée par la modération', 'success');

        // Créer un nouveau File avec des métadonnées supplémentaires
        const modifiedFile = new File([file], file.name, {
          type: file.type,
          lastModified: file.lastModified
        });

        // Ajouter l'ID temporaire comme propriété personnalisée
        Object.defineProperty(modifiedFile, 'tempImageId', {
          value: tempImageId,
          writable: false
        });

        onUploadPhoto(modifiedFile);
        onClose();
      } else {
        // L'image est inappropriée
        // Garder la modale ouverte et afficher le message de rejet
        // La modale reste ouverte pour afficher le message détaillé

        // Notification simple
        notify('Image refusée : ne respecte pas nos règles de modération', 'error');

        // Les états seront réinitialisés quand l'utilisateur fermera la modale
      }
    } catch (error) {
      logger.error('Erreur lors de la modération de l\'image', error);
      notify('Erreur lors de la vérification de l\'image. Veuillez réessayer.', 'error');
      setSelectedFile(null);
      setPreviewUrl(null);
      setModerationResult(null);
      setIsModerationModalOpen(false);
    }

    return () => {
      if (objectUrl) URL.revokeObjectURL(objectUrl);
    };
  };

  if (!isOpen) return null;

  return (
    <ModalPortal>
      <div className="fixed inset-0 z-50 flex items-center justify-center">
        {/* Overlay avec gestion du clic */}
        <div
          className="fixed inset-0"
          onClick={onClose}
          aria-label="Fermer la modale"
        />
        {/* Contenu de la modale avec scrollable pour mobile */}
        <div
          className="relative bg-white rounded-2xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto transition-all duration-300 scale-100 m-4"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header avec onglets - fixe pour rester visible pendant le défilement */}
          <div className="border-b border-gray-100 bg-gradient-to-r from-[#FFF8F3] to-white sticky top-0 z-10 shadow-sm">
            <div className="flex justify-between items-center px-4 sm:px-8 pt-4 sm:pt-6">
              <h2 className="text-lg sm:text-2xl font-bold text-gray-800 leading-tight">{title}</h2>
              <button
                onClick={onClose}
                className="p-2 hover:bg-white/80 rounded-full transition-all duration-300"
                aria-label="Fermer"
              >
                <X className="h-5 w-5 sm:h-6 sm:w-6 text-gray-600" />
              </button>
            </div>

            {/* Onglets améliorés - responsive pour petits écrans */}
            <div className="flex flex-col sm:flex-row mt-4 sm:mt-6 px-4 sm:px-6">
              <button
                onClick={() => setActiveTab('upload')}
                className={`flex items-center gap-2 px-4 sm:px-6 py-3 sm:py-4 font-medium text-sm transition-all duration-300 relative w-full ${isBannerMode ? 'sm:w-1/2' : 'sm:w-1/3'} justify-center rounded-t-lg mb-2 sm:mb-0
                  ${activeTab === 'upload'
                    ? 'text-white bg-[#FF6B2C] border-b-2 border-[#FF6B2C]'
                    : 'text-[#FF6B2C] bg-[#FFF8F3] hover:bg-[#FFD7BE] hover:shadow-lg border border-gray-200 transition-transform transform hover:scale-105'}`}
              >
                <Upload className={`h-5 w-5 transition-colors duration-300 ${activeTab === 'upload' ? 'text-white' : '#FF6B2C'}`} />
                <span className="font-semibold">Uploader une photo</span>
              </button>
              {!isBannerMode && (
                <button
                  onClick={() => {
                    setActiveTab('avatar');
                    // Déclencher un chargement initial si nécessaire
                    if (displayedAvatars.length < avatars.length && !loading) {
                      setTimeout(loadMore, 100); // Petit délai pour laisser le DOM se mettre à jour
                    }
                  }}
                  className={`flex items-center gap-2 px-4 sm:px-6 py-3 sm:py-4 font-medium text-sm transition-all duration-300 relative w-full sm:w-1/3 justify-center rounded-t-lg mb-2 sm:mb-0
                    ${activeTab === 'avatar'
                      ? 'text-white bg-[#FF6B2C] border-b-2 border-[#FF6B2C]'
                      : 'text-[#FF6B2C] bg-[#FFF8F3] hover:bg-[#FFD7BE] hover:shadow-lg border border-gray-200 transition-transform transform hover:scale-105'}`}
                >
                  <Grid className={`h-5 w-5 transition-colors duration-300 ${activeTab === 'avatar' ? 'text-white' : '#FF6B2C'}`} />
                  <span className="font-semibold">Choisir un avatar</span>
                </button>
              )}
              <button
                onClick={() => setActiveTab('ai')}
                className={`flex items-center gap-2 px-4 sm:px-6 py-3 sm:py-4 font-medium text-sm transition-all duration-300 relative w-full ${isBannerMode ? 'sm:w-1/2' : 'sm:w-1/3'} justify-center rounded-t-lg
                  ${activeTab === 'ai'
                    ? 'text-white bg-[#FF6B2C] border-b-2 border-[#FF6B2C]'
                    : 'text-[#FF6B2C] bg-[#FFF8F3] hover:bg-[#FFD7BE] hover:shadow-lg border border-gray-200 transition-transform transform hover:scale-105'}`}
              >
                <Sparkles className={`h-5 w-5 transition-colors duration-300 ${activeTab === 'ai' ? 'text-white' : '#FF6B2C'}`} />
                <span className="font-semibold">Générer avec IA</span>
              </button>
            </div>
          </div>

          {/* Contenu des onglets - padding adapté aux petits écrans */}
          <div className="p-4 sm:p-8">
            {activeTab === 'upload' ? (
              <div className="flex flex-col items-center justify-center py-4 sm:py-8">
                <input
                  type="file"
                  ref={fileInputRef}
                  onChange={handleFileUpload}
                  accept="image/jpeg,image/png,image/webp"
                  className="hidden"
                />
                <div
                  onClick={() => fileInputRef.current?.click()}
                  className="w-full max-w-md cursor-pointer group"
                >
                  <div className="mb-4 sm:mb-8 text-center p-4 sm:p-10 border-2 border-dashed border-gray-200 rounded-2xl transition-all duration-300 hover:border-[#FF6B2C] hover:bg-[#FFF8F3]">
                    <div className="mb-3 sm:mb-4 p-3 sm:p-4 bg-[#FFF8F3] rounded-full w-16 h-16 sm:w-20 sm:h-20 mx-auto group-hover:bg-white transition-all duration-300">
                      <Upload className="h-10 w-10 sm:h-12 sm:w-12 text-[#FF6B2C] mx-auto" />
                    </div>
                    <h3 className="text-lg sm:text-xl font-semibold text-gray-900 mb-4 sm:mb-6">{uploadLabel}</h3>
                    <button
                      className="px-4 sm:px-6 py-2 sm:py-3 bg-[#FF6B2C] text-white rounded-xl hover:bg-[#FF7A35] transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105 font-medium"
                    >
                      Parcourir mes fichiers
                    </button>
                    <p className="mt-3 sm:mt-4 text-xs sm:text-sm text-gray-500">
                      Formats acceptés : JPG, PNG ou WEBP (max. 5MB)
                    </p>
                    <p className="mt-1 sm:mt-2 text-xs text-gray-400">
                      Votre image sera analysée par notre système de modération IA
                    </p>
                  </div>
                </div>
                {/* Sélecteur de position de la bannière */}
                {isBannerMode && (
                  <div className="mt-4 w-full max-w-xs mx-auto">
                    <label htmlFor="banner-position" className="block text-sm font-medium text-gray-700 mb-2">
                      Position de l'image dans la bannière :
                    </label>
                    <select
                      id="banner-position"
                      value={selectedBannerPosition}
                      onChange={(e) => {
                        const newPosition = e.target.value as 'top' | 'center' | 'bottom';
                        setSelectedBannerPosition(newPosition);
                        if (onSelectBannerPosition) {
                          onSelectBannerPosition(newPosition);
                        }
                      }}
                      className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-[#FF6B2C] focus:border-[#FF6B2C] sm:text-sm rounded-md"
                    >
                      <option value="top">Haut</option>
                      <option value="center">Centre</option>
                      <option value="bottom">Bas</option>
                    </select>
                  </div>
                )}
              </div>
            ) : activeTab === 'avatar' ? (
              <div ref={containerRef} className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 gap-4 overflow-y-auto max-h-[60vh]">
                {loading && displayedAvatars.length === 0 ? (
                  <div className="flex justify-center items-center h-40 col-span-full">
                    <div className="animate-spin rounded-full h-10 w-10 border-4 border-[#FF6B2C]/20 border-l-[#FF6B2C]"></div>
                  </div>
                ) : (
                  <>
                    {displayedAvatars.map((avatarUrl, index) => (
                      <button
                        key={`${avatarUrl}-${index}`}
                        onClick={() => handleAvatarClick(avatarUrl)}
                        className="relative group aspect-square rounded-xl overflow-hidden hover:ring-4 hover:ring-[#FF6B2C]/20 hover:ring-offset-2 transition-all duration-300"
                      >
                        <img
                          src={DOMPurify.sanitize(avatarUrl)}
                          alt={`Avatar ${index + 1}`}
                          className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
                          loading="lazy"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-end justify-center pb-4">
                          <span className="text-white text-sm font-medium">Sélectionner</span>
                        </div>
                      </button>
                    ))}
                    {/* Indicateur de chargement automatique */}
                    {displayedAvatars.length < avatars.length && (
                      <div className="flex justify-center items-center py-4 col-span-full">
                        <div className="animate-spin rounded-full h-6 w-6 border-4 border-[#FF6B2C]/20 border-l-[#FF6B2C]"></div>
                      </div>
                    )}
                  </>
                )}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-4 sm:py-8">
                <div className="w-full max-w-md">
                  <div className="mb-6 text-center">
                    <div className="mb-4 p-4 bg-[#FFF8F3] rounded-full w-20 h-20 mx-auto">
                      <Sparkles className="h-12 w-12 text-[#FF6B2C] mx-auto" />
                    </div>
                    {hasFullName ? (
                      <>
                        <h3 className="text-xl font-semibold text-gray-900 mb-2">
                          {isBannerMode
                            ? "Générer une bannière de profil avec l'IA"
                            : "Générer une photo de profil avec l'IA"}
                        </h3>
                        <p className="text-sm text-gray-600 mb-6">
                          {isBannerMode
                            ? "Utilisez l'intelligence artificielle pour créer une bannière de profil attrayante. Cette opération coûte 5 crédits IA."
                            : "Utilisez l'intelligence artificielle pour créer une photo de profil professionnelle. Cette opération coûte 5 crédits IA."}
                        </p>

                        {/* Vérification du consentement IA */}
                        {consentLoading ? (
                          <div className="flex justify-center">
                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#FF6B2C]"></div>
                          </div>
                        ) : !hasConsent ? (
                          <div className="mb-6 p-4 bg-orange-50 border border-orange-200 rounded-lg">
                            <p className="text-sm text-orange-800 mb-3 text-center">
                              Vous devez accepter les conditions d'utilisation de l'IA avant de pouvoir générer du contenu.
                            </p>
                            <div className="flex justify-center">
                              <button
                                onClick={() => window.dispatchEvent(new CustomEvent('open-ai-consent-modal'))}
                                className="px-4 py-2 bg-[#FF6B2C] text-white rounded-lg hover:bg-[#FF7A35] transition-colors text-sm font-medium"
                              >
                                Accepter les CGU
                              </button>
                            </div>
                          </div>
                        ) : (
                          <div className="flex justify-center">
                            <AiImageGenerationButton
                              purpose={isBannerMode ? "banner_picture" : "profile_picture"}
                              onImageGenerated={async (imageUrl) => {
                                try {
                                  const blob = await fetch(imageUrl).then(res => res.blob());
                                  if (isBannerMode) {
                                    // Pour les bannières générées par IA, compresser côté frontend avec les specs bannière
                                    const file = new File([blob], "banner_ia.jpg", { type: blob.type });
                                    const compressedFile = await compressBannerPhoto(file);
                                    onUploadPhoto(compressedFile);
                                  } else {
                                    // Pour la photo de profil générée par IA, compresser avec les specs profil
                                    const file = new File([blob], "profile_picture_ia.jpg", { type: blob.type });
                                    const compressedFile = await compressProfilPhoto(file);
                                    onUploadPhoto(compressedFile);
                                  }
                                } catch (error) {
                                  logger.error('Erreur lors du traitement de l\'image IA générée', error);
                                  notify('Erreur lors du traitement de l\'image générée. Veuillez réessayer.', 'error');
                                }
                                onClose();
                              }}
                              variant="contained"
                              color="primary"
                              size="large"
                              width={isBannerMode ? 1152 : undefined}
                              height={isBannerMode ? 640 : undefined}
                            />
                          </div>
                        )}
                      </>
                    ) : (
                      <>
                        <h3 className="text-xl font-semibold text-gray-900 mb-2">Profil incomplet</h3>
                        <p className="text-sm text-gray-600 mb-6">
                          Vous devez renseigner votre <span className="font-bold text-[#FF6B2C]">nom</span> et <span className="font-bold text-[#FF6B2C]">prénom</span> dans votre profil avant de pouvoir générer une photo de profil avec l'IA.<br />
                        </p>
                        <button
                          className="px-6 py-3 bg-[#FF6B2C] text-white rounded-lg hover:bg-[#FF7A35] transition-all duration-300 shadow-md hover:shadow-xl mb-2"
                          onClick={() => window.location.reload()}
                        >
                          Rafraîchir
                        </button>
                      </>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      <AvatarPreviewModal
        isOpen={showPreview}
        onClose={() => setShowPreview(false)}
        avatars={avatars}
        selectedAvatar={selectedAvatar}
        onSelectAvatar={onSelectAvatar}
        onPreviewChange={setSelectedAvatar}
      />

      {/* Modal de modération d'image séparée */}
      <ModalPortal isOpen={isModerationModalOpen}>
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          <div
            className="fixed inset-0 bg-black/50"
            onClick={() => {
              // Toujours permettre l'annulation, même pendant le chargement
              setIsModerationModalOpen(false);
              if (previewUrl) {
                URL.revokeObjectURL(previewUrl);
                setPreviewUrl(null);
              }
              setSelectedFile(null);
              setModerationResult(null);
            }}
          />
          <div className="relative bg-white rounded-2xl shadow-2xl w-[calc(100%-32px)] sm:w-full max-w-lg overflow-hidden flex flex-col">
            <div className="p-4 pt-5 flex justify-between items-center border-b">
              <h3 className="text-lg font-semibold">
                {isModerationLoading
                  ? "Analyse de sécurité en cours"
                  : "Modération de la photo"}
              </h3>
              <button
                onClick={() => {
                  // Toujours permettre l'annulation, même pendant le chargement
                  setIsModerationModalOpen(false);
                  if (previewUrl) {
                    URL.revokeObjectURL(previewUrl);
                    setPreviewUrl(null);
                  }
                  setSelectedFile(null);
                  setModerationResult(null);
                }}
                className="p-2 hover:bg-gray-100 rounded-full"
              >
                <X className="h-5 w-5 text-gray-500" />
              </button>
            </div>
            <div className="flex-1 overflow-y-auto max-h-[calc(90vh-80px)]">
              {isModerationLoading ? (
                <ImageModerationStatus
                  isLoading={true}
                  imageUrl={previewUrl || undefined}
                  title="Analyse de sécurité en cours"
                  onCancel={() => {
                    // Toujours permettre l'annulation, même pendant le chargement
                    setIsModerationModalOpen(false);
                    if (previewUrl) {
                      URL.revokeObjectURL(previewUrl);
                      setPreviewUrl(null);
                    }
                    setSelectedFile(null);
                    setModerationResult(null);
                  }}
                />
              ) : (
                <div className="p-6">
                  {/* Afficher l'image refusée */}
                  {previewUrl && (
                    <div className="mb-6 flex justify-center">
                      <div className="relative w-32 h-32 sm:w-40 sm:h-40 rounded-full overflow-hidden border-4 border-white shadow-lg">
                        <img
                          src={previewUrl}
                          alt="Image refusée"
                          className="w-full h-full object-cover"
                        />
                        <div className="absolute inset-0 bg-red-900/20"></div>
                      </div>
                    </div>
                  )}

                  {/* Message de rejet détaillé */}
                  <RejectedImageMessage
                    contentType="profile_picture"
                    description={moderationResult?.description}
                    improvementSuggestions={moderationResult?.improvementSuggestions}
                    variant="detailed"
                  />

                  {/* Bouton pour réessayer */}
                  <div className="mt-6 flex justify-center">
                    <button
                      onClick={() => {
                        setIsModerationModalOpen(false);
                        if (previewUrl) {
                          URL.revokeObjectURL(previewUrl);
                          setPreviewUrl(null);
                        }
                        setSelectedFile(null);
                        setModerationResult(null);
                        // Ouvrir automatiquement le sélecteur de fichier
                        setTimeout(() => fileInputRef.current?.click(), 300);
                      }}
                      className="px-6 py-3 bg-[#FF6B2C] text-white rounded-lg hover:bg-[#FF7A35] transition-all duration-300 shadow-md hover:shadow-xl"
                    >
                      Choisir une autre photo
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </ModalPortal>
    </ModalPortal>
  );
};

export default AvatarModal;