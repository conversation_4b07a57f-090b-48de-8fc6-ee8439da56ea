import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  Checkbox,
  FormControlLabel,
  TextField,
  Paper,
  CircularProgress,
  Alert,
  IconButton,
  Link as MuiLink
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { motion } from 'framer-motion';
import ModalPortal from '../ModalPortal';
import { X, Info, CheckCircle2, AlertTriangle } from 'lucide-react';
import { notify } from '../Notification';
import logger from '@/utils/logger';
import { getCommonHeaders } from '@/utils/headers';
import { setCookie } from '@/utils/cookieUtils';
import axios from 'axios';
import { API_CONFIG } from '@/config/api';
import DOMPurify from 'dompurify';
import { useAuth } from '@/contexts/AuthContext';
import { Link } from 'react-router-dom';

// Définir l'interface pour l'utilisateur avec les propriétés nom et prénom
interface ExtendedUser {
  id?: string;
  email?: string;
  userType?: string;
  profil_actif?: boolean;
  role?: string;
  nom?: string | null;
  prenom?: string | null;
  profil?: any;
}

// Styles personnalisés
const StyledPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  maxWidth: '700px',
  width: '100%',
  borderRadius: '12px',
  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
  maxHeight: '90vh',
  display: 'flex',
  flexDirection: 'column'
}));

const ContentBox = styled(Box)(({ theme }) => ({
  flex: 1,
  overflowY: 'auto',
  marginBottom: theme.spacing(2),
  padding: theme.spacing(1)
}));

const ButtonBox = styled(Box)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  marginTop: theme.spacing(2),
  [theme.breakpoints.down('sm')]: {
    flexDirection: 'column',
    gap: theme.spacing(1)
  }
}));

const StyledButton = styled(Button)(() => ({
  borderRadius: '8px',
  padding: '10px 20px',
  fontWeight: 600,
  textTransform: 'none',
  boxShadow: 'none',
  '&:hover': {
    boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)'
  }
}));

interface AiConsentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAccept: () => void;
}

const COOKIE_NAME = 'ai_consent_accepted';
const COOKIE_EXPIRY = 365 * 24 * 60 * 60; // 1 an en secondes

const AiConsentModal: React.FC<AiConsentModalProps> = ({ isOpen, onClose, onAccept }) => {
  const { user } = useAuth() as { user: ExtendedUser };
  const [accepted, setAccepted] = useState(false);
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [profileComplete, setProfileComplete] = useState(true);

  // Fonction pour initialiser les données du profil utilisateur
  const initializeUserProfile = async () => {
    if (!user?.id) return;

    try {
      setLoading(true);

      // Récupérer les données du profil depuis l'API
      const headers = await getCommonHeaders();
      const response = await axios.get(`${API_CONFIG.baseURL}/api/users/profil`, {
        headers,
        withCredentials: true
      });

      console.log('🔍 Réponse API profil:', response.data);

      if (response.data?.success && response.data?.profil) {
        const profil = response.data.profil;
        console.log('📋 Données profil:', profil);

        const prenom = profil.firstName || profil.prenom || '';
        const nom = profil.lastName || profil.nom || '';

        console.log('👤 Nom/Prénom extraits:', { prenom, nom });

        setFirstName(prenom);
        setLastName(nom);

        // Vérifier si le profil est complet
        if (!prenom || !nom) {
          setProfileComplete(false);
          setError('Vous devez compléter votre profil (nom et prénom) avant de pouvoir donner votre consentement à l\'utilisation de l\'IA.');
        } else {
          setProfileComplete(true);
          setError(null);
        }
      } else {
        // Fallback sur les données du contexte d'authentification
        const prenom = user.prenom || '';
        const nom = user.nom || '';

        setFirstName(prenom);
        setLastName(nom);

        if (!prenom || !nom) {
          setProfileComplete(false);
          setError('Vous devez compléter votre profil (nom et prénom) avant de pouvoir donner votre consentement à l\'utilisation de l\'IA.');
        } else {
          setProfileComplete(true);
          setError(null);
        }
      }
    } catch (err) {
      logger.warn('Erreur lors de l\'initialisation du profil:', err);
      // Fallback sur les données du contexte d'authentification
      const prenom = user.prenom || '';
      const nom = user.nom || '';

      setFirstName(prenom);
      setLastName(nom);

      if (!prenom || !nom) {
        setProfileComplete(false);
        setError('Vous devez compléter votre profil (nom et prénom) avant de pouvoir donner votre consentement à l\'utilisation de l\'IA.');
      } else {
        setProfileComplete(true);
        setError(null);
      }
    } finally {
      setLoading(false);
    }
  };

  // Le blocage du scroll est déjà géré par le composant ModalPortal

  useEffect(() => {
    // Réinitialiser les états quand la modale s'ouvre
    if (isOpen) {
      setAccepted(false);
      setError(null);

      // Initialiser les données du profil de manière asynchrone
      initializeUserProfile();
    }
  }, [isOpen, user]);

  const handleAccept = async () => {
    // Validation des champs
    if (!accepted) {
      setError('Vous devez accepter les conditions d\'utilisation de l\'IA');
      return;
    }

    if (!profileComplete) {
      setError('Vous devez compléter votre profil (nom et prénom) avant de pouvoir donner votre consentement à l\'utilisation de l\'IA.');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Sanitize les entrées utilisateur
      const sanitizedFirstName = DOMPurify.sanitize(firstName.trim());
      const sanitizedLastName = DOMPurify.sanitize(lastName.trim());

      // Enregistrer le consentement dans la base de données
      const headers = await getCommonHeaders();
      await axios.post(
        `${API_CONFIG.baseURL}/api/user/ai-consent`,
        {
          firstName: sanitizedFirstName,
          lastName: sanitizedLastName
        },
        {
          headers,
          withCredentials: true
        }
      );

      // Enregistrer le consentement dans un cookie
      setCookie(COOKIE_NAME, 'true', COOKIE_EXPIRY, true);

      // Déclencher un event global pour MAJ le consentement partout
      window.dispatchEvent(new CustomEvent('ai-consent-updated'));

      // Fermer la modale et appeler le callback
      onAccept();
    } catch (err) {
      logger.error('Erreur lors de l\'enregistrement du consentement IA:', err);
      setError('Une erreur est survenue lors de l\'enregistrement de votre consentement. Veuillez réessayer.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <ModalPortal isOpen={isOpen} onBackdropClick={() => {}}>
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        transition={{ duration: 0.2 }}
      >
        <StyledPaper>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h5" sx={{ fontWeight: 700, color: '#FF6B2C' }}>
              Consentement à l'utilisation de l'Intelligence Artificielle
            </Typography>
            <IconButton onClick={onClose} size="small" sx={{ color: '#FF6B2C' }}>
              <X size={20} />
            </IconButton>
          </Box>

          <Alert severity="info" sx={{ mb: 2 }}>
            <Typography variant="body2">
              Conformément à la législation européenne, nous avons besoin de votre consentement explicite avant d'utiliser des technologies d'intelligence artificielle.
            </Typography>
          </Alert>

          <ContentBox>
            {loading && (
              <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', py: 2 }}>
                <CircularProgress size={30} sx={{ color: '#FF6B2C' }} />
                <Typography variant="body2" sx={{ ml: 2 }}>
                  Chargement de vos informations...
                </Typography>
              </Box>
            )}

            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <CheckCircle2 size={24} color="#FF6B2C" />
              <Typography variant="h6" sx={{ ml: 1, fontWeight: 600 }}>
                Consentement explicite à l'utilisation de l'Intelligence Artificielle (IA)
              </Typography>
            </Box>

            <Typography variant="body2" sx={{ mb: 2 }}>
              Conformément à la législation européenne (notamment le Règlement Général sur la Protection des Données - RGPD, la loi sur les services numériques - DSA, et les recommandations de la CNIL), l'utilisation de nos services impliquant une technologie d'intelligence artificielle nécessite votre consentement explicite, libre, éclairé et spécifique.
            </Typography>

            <Box sx={{ bgcolor: '#FFF8F3', p: 2, borderRadius: 2, mb: 3, border: '1px solid #FFE4BA' }}>
              <Typography variant="subtitle1" sx={{ fontWeight: 600, display: 'flex', alignItems: 'center', mb: 1 }}>
                <Info size={20} className="mr-2" color="#FF6B2C" />
                Informations importantes à lire attentivement :
              </Typography>

              <Typography variant="body2" sx={{ mb: 2 }}>
                En cochant cette case et en renseignant votre nom et prénom, vous reconnaissez et acceptez expressément que :
              </Typography>

              <Typography variant="body2" sx={{ fontWeight: 600, mb: 1 }}>
                Utilisation d'un système d'IA :
              </Typography>
              <Typography variant="body2" sx={{ mb: 2, pl: 2 }}>
                Vous êtes informé(e) que certaines fonctionnalités de notre plateforme font appel à des systèmes d'intelligence artificielle (exemples : création de mission automatisée, génération de contenu, amélioration de réponses, assistance rédactionnelle, etc.).
              </Typography>

              <Typography variant="body2" sx={{ fontWeight: 600, mb: 1 }}>
                Traitement des données personnelles :
              </Typography>
              <Typography variant="body2" sx={{ mb: 0.5, pl: 2 }}>
                • Vos données de profil (ex. : nom, prénom, email, contenu saisi, préférences, etc.) peuvent être transmises à des fournisseurs tiers d'IA pour permettre le bon fonctionnement du service.
              </Typography>
              <Typography variant="body2" sx={{ mb: 0.5, pl: 2 }}>
                • Ces données pourront faire l'objet d'un traitement algorithmique automatisé, en dehors de notre contrôle direct, notamment si les serveurs des partenaires sont situés hors de l'Union européenne.
              </Typography>
              <Typography variant="body2" sx={{ mb: 2, pl: 2 }}>
                • Aucune donnée sensible (ex. : données de santé, opinions politiques, etc.) ne doit être saisie par vos soins dans les champs de texte libres.
              </Typography>

              <Typography variant="body2" sx={{ fontWeight: 600, mb: 1 }}>
                Finalités du traitement :
              </Typography>
              <Typography variant="body2" sx={{ mb: 2, pl: 2 }}>
                Ces traitements ont pour finalité de générer des contenus personnalisés, d'améliorer l'expérience utilisateur, et d'automatiser certaines tâches à votre demande.
              </Typography>

              <Typography variant="body2" sx={{ fontWeight: 600, mb: 1 }}>
                Durée de conservation et validité du consentement :
              </Typography>
              <Typography variant="body2" sx={{ mb: 0.5, pl: 2 }}>
                • Les données traitées par ou via des IA sont conservées uniquement pour la durée nécessaire à la prestation, sauf obligation légale ou demande contraire de votre part.
              </Typography>
              <Typography variant="body2" sx={{ mb: 2, pl: 2 }}>
                • Votre consentement est valable pour une durée de 6 mois à compter de la date d'acceptation. À l'issue de cette période, il vous sera demandé de renouveler votre consentement pour continuer à utiliser les fonctionnalités d'IA.
              </Typography>

              <Typography variant="body2" sx={{ fontWeight: 600, mb: 1 }}>
                Absence de garantie sur le comportement des IA :
              </Typography>
              <Typography variant="body2" sx={{ mb: 0.5, pl: 2 }}>
                • Bien que nous sélectionnions rigoureusement nos partenaires techniques, nous ne pouvons garantir le comportement ou les usages des IA tierces sur vos données.
              </Typography>
              <Typography variant="body2" sx={{ mb: 2, pl: 2 }}>
                • L'usage de l'IA s'effectue à vos risques et périls, dans le respect de nos CGU et de notre politique de confidentialité.
              </Typography>

              <Typography variant="body2" sx={{ fontWeight: 600, mb: 1 }}>
                Vos droits :
              </Typography>
              <Typography variant="body2" sx={{ mb: 0.5, pl: 2 }}>
                • Vous disposez à tout moment d'un droit d'accès, de rectification, d'opposition, de suppression, et de limitation du traitement de vos données, conformément aux articles 12 à 23 du RGPD.
              </Typography>
              <Typography variant="body2" sx={{ mb: 2, pl: 2 }}>
                • Vous pouvez retirer votre consentement à tout moment en contactant notre DPO à <EMAIL>.
              </Typography>
            </Box>

            <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2 }}>
              Pour continuer, veuillez :
            </Typography>

            <Box sx={{ mb: 3 }}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={accepted}
                    onChange={(e) => setAccepted(e.target.checked)}
                    sx={{
                      color: '#FF6B2C',
                      '&.Mui-checked': {
                        color: '#FF6B2C',
                      },
                    }}
                  />
                }
                label={
                  <Typography variant="body2">
                    Je reconnais avoir lu, compris et accepté les conditions ci-dessus, et je consens à l'utilisation de mes données dans le cadre des fonctionnalités d'intelligence artificielle proposées.
                  </Typography>
                }
              />
            </Box>

            <Box sx={{ display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, gap: 2, mb: 2 }}>
              <TextField
                label="Prénom"
                variant="outlined"
                fullWidth
                value={firstName}
                size="small"
                required
                disabled
                helperText={!firstName ? "Prénom manquant dans votre profil" : ""}
                error={!firstName}
              />
              <TextField
                label="Nom"
                variant="outlined"
                fullWidth
                value={lastName}
                size="small"
                required
                disabled
                helperText={!lastName ? "Nom manquant dans votre profil" : ""}
                error={!lastName}
              />
            </Box>

            {!profileComplete && (
              <Alert
                severity="warning"
                icon={<AlertTriangle size={20} />}
                sx={{ mb: 2 }}
              >
                Votre profil n'est pas complet. Veuillez <strong>compléter votre nom et prénom</strong> dans votre <Link to="/dashboard/profil" style={{ color: '#FF6B2C', textDecoration: 'underline' }}>profil utilisateur</Link> avant de pouvoir donner votre consentement à l'IA.<br />
                <span style={{ display: 'inline-block', marginTop: 8 }}>
                  Si vous venez de mettre à jour votre nom et prénom, <a href="#" onClick={(e) => { e.preventDefault(); window.location.reload(); }} style={{ color: '#FF6B2C', textDecoration: 'underline' }}>cliquez ici pour recharger la page</a>.
                </span>
              </Alert>
            )}

            {error && error !== 'Vous devez compléter votre profil (nom et prénom) avant de pouvoir donner votre consentement à l\'utilisation de l\'IA.' && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {error}
              </Alert>
            )}
          </ContentBox>

          <ButtonBox>
            <StyledButton
              variant="outlined"
              onClick={onClose}
              sx={{
                borderColor: '#FFE4BA',
                color: '#FF6B2C',
                '&:hover': { borderColor: '#FF6B2C', bgcolor: '#FFF8F3' }
              }}
            >
              Annuler
            </StyledButton>

            <StyledButton
              variant="contained"
              onClick={handleAccept}
              disabled={loading || !accepted || !profileComplete}
              sx={{
                bgcolor: '#FF6B2C',
                '&:hover': { bgcolor: '#FF7A35' },
                '&.Mui-disabled': { bgcolor: '#FFE4BA', color: '#FF965E' }
              }}
            >
              {loading ? (
                <CircularProgress size={24} sx={{ color: 'white' }} />
              ) : (
                "J'accepte et je continue"
              )}
            </StyledButton>
          </ButtonBox>
        </StyledPaper>
      </motion.div>
    </ModalPortal>
  );
};

export default AiConsentModal;
