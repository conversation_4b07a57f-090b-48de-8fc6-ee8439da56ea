# JobPartiel - Documentation Technique Complète

## 1. Vue d'ensemble du Projet

### 1.1 Objectif
Plateforme de mise en relation entre jobbeurs et clients pour des services de jardinage, bricolage et garde d'animaux, avec système de d'échange/troc (Jobi).

### 1.2 Architecture Technique
- **Frontend**: 
  - React 18.3.1 + Vite + TypeScript
  - Material UI 5.11
  - Tailwind CSS
  - Framer Motion pour les animations
  - React Router DOM 7.0.2
  - Recharts pour les graphiques
- **Backend**: 
  - Node.js + Express 5
  - TypeScript
  - Winston pour les logs
  - Express Validator et Zod pour la validation
- **Base de données**: 
  - Supabase (@supabase/supabase-js 2.46.2)
  - Gestion complète des profils utilisateurs
  - Système de notation et reviews
  - Gestion des abonnements
- **Cache**: 
  - Redis (ioredis 5.3.2)
  - Mode singleton
  - Support TLS
- **Proxy**: 
  - <PERSON>in<PERSON> (port 443)
  - Configuration CORS
  - Headers de sécurité

## 2. Infrastructure

### 2.1 Base de Données Supabase
Tables principales :
- users
  - Authentification
  - Gestion des rôles (jobpadm, jobmodo, jobutil)
  - Vérification email et profil
- user_profil
  - Informations détaillées
  - Documents vérifiés
- user_abo
  - Gestion des abonnements
  - Types : basic, premium, pro
- user_reviews
  - Système de notation
  - Commentaires vérifiés
- auth_tokens
  - Gestion JWT
  - Blacklisting
- password_resets
  - Sécurité renforcée
  - Limitation tentatives

### 2.2 Stockage
- **OpenStreetMap**:
  - Bucket: "osm"
  - URL: "https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
  - Composant: InterventionZoneMap.tsx
- **Supabase Storage**:
  - Bucket: "photo_profil"
  - URL: "https://api.jobpartiel.fr/api/storage-proxy/photo_profil/"
  - Gestion optimisée des images (Sharp)

### 2.3 Système de Cache Redis
- Sessions utilisateurs
- Blacklisting tokens
- Historique activités
- Configuration dynamique
- Stratégie de persistance
- Variables d'environnement

## 3. Sécurité

### 3.1 Authentification
- JWT (jsonwebtoken 9.0.2)
- bcryptjs pour le hashing
- Protection force brute
- MFA
- Sessions Redis
- Validation email

### 3.2 Sécurité Web
- Helmet
- CORS configuré
- Headers sécurité
- DOMPurify obligatoire
- Express Rate Limit
- Cookie-Parser sécurisé
- Protection XSS/CSRF
- Cookies HttpOnly

### 3.3 Monitoring
- Sentry
- Google Analytics
- Winston Logger
  - Rotation quotidienne
  - Niveaux de logs
  - Sans données sensibles

## 4. Frontend

### 4.1 Composants et État
- Material UI + Headless UI
- Gestion d'état:
  - React Hook Form
  - Context API
  - Zustand
  - État immutable

### 4.2 Performance
- Code splitting
- Lazy loading
- useMemo/useCallback
- Compression images
- Optimisation bundle

### 4.3 UI/UX
- Palette:
  - #FF6B2C (primaire)
  - #FF7A35 (secondaire)
  - #FF965E (tertiaire)
  - #FFF8F3 (background)
  - #FFE4BA (accent)
- Composants Material UI v5 !
- Animations Framer Motion
- Responsive design

### 4.4 Utilisation des Portails pour les Modales
L'utilisation de `createPortal` de React permet de rendre les modales directement dans le body du document, en dehors de la hiérarchie normale du DOM. Cela évite les problèmes de contexte d'empilement et de z-index qui peuvent survenir lorsque les modales sont rendues à l'intérieur d'autres composants.

Cette solution est préférable à la simple manipulation des z-index, car :
- Les modales sont complètement isolées du reste de l'application.
- Elles ne sont pas affectées par les contextes d'empilement des composants parents.
- C'est une solution plus propre et plus maintenable.
- C:\Users\<USER>\CascadeProjects\Github OK\frontend\src\components\ModalPortal.tsx

## 5. Fonctionnalités Métier

### 5.1 Système Monétaire (Jobi)
- Controllers: `/backend/src/controllers/jobi.ts`
- Routes: `/backend/src/routes/jobi.ts`
- Hook: `/frontend/src/hooks/useJobiBalance.ts`
- Transactions sécurisées
- Historique complet

### 5.2 Système de Notification
- Composant: `/frontend/src/components/Notification.tsx`
- Types personnalisés
- Gestion temps réel
- Persistance Redis

## 6. Développement

### 6.1 Standards
- TypeScript strict
- ESLint + Prettier
- Tests RTL

## 7. Accessibilité
- WCAG 2.1 AA
- Lecteurs d'écran
- Navigation clavier
- ARIA
- Support multilingue

## 8. Procédures
1. Scanner backend/frontend
2. Vérifier package.json
3. Analyser schéma BDD
4. Utiliser l'existant
5. Maintenir la sécurité
6. Documenter en français


Consigne à toujours respecter : 
Toujours répondre en français, toujours par commencer a scanner le projet backend et frontend afin de le comprendre. 
Eviter de créer des fichiers inutiles pour garder un code propre, aéré et structuré. 
Utilise déja tout ce qui est disponible (les routes, l'api, supabase, etc)

Avant toute chose tu dois toujours scanner le code de backend et frontend afin de comprendre le projet. 
Puis ensuite scan le package.json et la base de données (C:\Users\<USER>\CascadeProjects\Github OK\Création des bases de données.sql) afin de comprendre le schema de base, les modules et leurs versions. 
Tu as toutes les informations du cahier des charges ici si tu souhaites plus d'informations : C:\Users\<USER>\CascadeProjects\Github OK\Prompt debut chat

Ne me demande jamais s'il faut executer ou appliquer les modifications, fait le directement !

Tu dois analyser le projet complet afin de comprendre la structure du projet. 
Tu dois rechercher : les routes, les API, le schema de base de données, le backend (supabase, etc), le frontend mais également regarder les fichiers package.json.

Tout en respectant le thème et ses couleurs tu dois toujours faire des modifications le plus simplement possible sans méthode d'ingénierie ou complexe.

Tout les appels API supabase etc doivent etre fait via API dans le backend !!!

Pour information :
Supabase est ici : C:\Users\<USER>\CascadeProjects\Github OK\backend\src\config\supabase.ts
Schema de base : C:\Users\<USER>\CascadeProjects\Github OK\Création des bases de données.sql

Ne me demande pas quoi faire, fait la modification directement sans me poser de questions.


Maintenant, ce que je te demande de faire, tu dois toujours utiliser write_to_file  pour écriture les modifications en faisant l'ajout/modif le plus simplement possible :