{"name": "job-partiel-server", "version": "1.0.0", "description": "", "main": "dist/server.js", "scripts": {"start": "node dist/server.js", "build": "tsc", "dev": "tsx watch src/server.ts", "optimize-images": "tsx src/scripts/optimize-images.ts", "optimize-images:cron": "tsx src/scripts/optimize-images.ts cron"}, "dependencies": {"@supabase/supabase-js": "^2.49.4", "@types/node-cron": "^3.0.11", "@types/passport": "^1.0.17", "@types/passport-google-oauth20": "^2.0.16", "axios": "^1.8.4", "bcryptjs": "^3.0.2", "compression": "^1.8.0", "connect-redis": "^8.0.3", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "date-fns": "^4.1.0", "dompurify": "^3.2.5", "dotenv": "^16.5.0", "exceljs": "^4.4.0", "express": "^5.1.0", "express-fileupload": "^1.5.1", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^7.5.0", "express-session": "^1.18.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "hpp": "^0.2.3", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.2", "node-cron": "^3.0.3", "nodemailer": "^6.10.1", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "pdfkit": "^0.17.0", "sharp": "^0.34.1", "socket.io": "^4.8.1", "stripe": "^18.0.0", "winston": "^3.17.0", "zod": "^3.24.3"}, "devDependencies": {"@types/compression": "^1.7.5", "@types/cookie-parser": "^1.4.8", "@types/cors": "^2.8.17", "@types/express": "^5.0.1", "@types/express-fileupload": "^1.5.1", "@types/express-session": "^1.18.1", "@types/hpp": "^0.2.6", "@types/jsonwebtoken": "^9.0.9", "@types/multer": "^1.4.12", "@types/node": "^22.14.1", "@types/nodemailer": "^6.4.17", "@types/pdfkit": "^0.13.9", "tsx": "^4.19.3", "typescript": "^5.8.3"}}