import { Request, Response } from 'express';
import { supabase } from '../config/supabase';
import logger from '../utils/logger';
import { redis } from '../config/redis';
import { encryptClientDataAsync, decryptClientDataAsync } from '../utils/encryption';
import ExcelJS from 'exceljs';

// Constantes pour le cache
const CLIENT_CACHE_PREFIX = 'client_controller:';
const CLIENT_LIST_CACHE_PREFIX = 'client_list_controller:';
const CACHE_TTL = 180; // 3 minutes en secondes

export const clientController = {
  // Fonction utilitaire pour valider l'UUID
  validateUUID: (uuid: string): boolean => {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
  },

  // Récupérer tous les clients
  getClients: async (req: Request, res: Response) => {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        res.status(401).json({ success: false, message: 'Non autorisé' });
        return;
      }

      // Vérifier si les données sont en cache
      const cacheKey = `${CLIENT_LIST_CACHE_PREFIX}${userId}`;
      const cachedClients = await redis.get(cacheKey);
      
      if (cachedClients) {
        res.json({ success: true, data: JSON.parse(cachedClients) });
        return;
      }

      const { data: clients, error } = await supabase
        .from('invoices_client')
        .select('*')
        .eq('user_id', userId)
        .order('nom', { ascending: true });

      if (error) {
        logger.error('Erreur lors de la récupération des clients:', error);
        res.status(500).json({ success: false, message: 'Erreur lors de la récupération des clients' });
        return;
      }

      // Déchiffrer les données des clients avant de les retourner
      const decryptedClients = await Promise.all(clients?.map(client => decryptClientDataAsync(client)) || []);

      // Mettre en cache les données déchiffrées pour 3 minutes
      await redis.setex(cacheKey, CACHE_TTL, JSON.stringify(decryptedClients));

      res.json({ success: true, data: decryptedClients });
    } catch (error) {
      logger.error('Erreur lors de la récupération des clients:', error);
      res.status(500).json({ success: false, message: 'Erreur serveur' });
    }
  },

  // Récupérer un client spécifique
  getClient: async (req: Request, res: Response) => {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        res.status(401).json({ success: false, message: 'Non autorisé' });
        return;
      }

      const { id } = req.params;
      if (!clientController.validateUUID(id)) {
        res.status(400).json({ success: false, message: 'ID client invalide' });
        return;
      }

      // Vérifier si les données sont en cache
      const cacheKey = `${CLIENT_CACHE_PREFIX}${userId}:${id}`;
      const cachedClient = await redis.get(cacheKey);
      
      if (cachedClient) {
        res.json({ success: true, data: JSON.parse(cachedClient) });
        return;
      }

      const { data: client, error } = await supabase
        .from('invoices_client')
        .select('*')
        .eq('id', id)
        .eq('user_id', userId)
        .single();

      if (error) {
        logger.error('Erreur lors de la récupération du client:', error);
        res.status(500).json({ success: false, message: 'Erreur lors de la récupération du client' });
        return;
      }

      if (!client) {
        res.status(404).json({ success: false, message: 'Client non trouvé' });
        return;
      }

      // Déchiffrer les données du client
      const decryptedClient = await decryptClientDataAsync(client);

      // Mettre en cache les données déchiffrées pour 3 minutes
      await redis.setex(cacheKey, CACHE_TTL, JSON.stringify(decryptedClient));

      res.json({ success: true, data: decryptedClient });
    } catch (error) {
      logger.error('Erreur lors de la récupération du client:', error);
      res.status(500).json({ success: false, message: 'Erreur serveur' });
    }
  },

  // Créer un nouveau client
  createClient: async (req: Request, res: Response) => {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        res.status(401).json({ success: false, message: 'Non autorisé' });
        return;
      }

      const { 
        nom, 
        email, 
        telephone, 
        adresse, 
        siret, 
        tva, 
        notes,
        forme_juridique,
        code_ape
      } = req.body;

      if (!nom) {
        res.status(400).json({ success: false, message: 'Le nom du client est requis' });
        return;
      }

      // Vérifier qu'au moins un moyen de contact est fourni
      if (!email && !telephone && !adresse) {
        res.status(400).json({ 
          success: false, 
          message: 'Au moins un moyen de contact (email, téléphone ou adresse) est requis' 
        });
        return;
      }

      // Validation de l'email
      if (email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
        res.status(400).json({ success: false, message: 'Format d\'email invalide' });
        return;
      }

      // Validation du SIRET (14 chiffres)
      if (siret && !/^\d{14}$/.test(siret)) {
        res.status(400).json({ success: false, message: 'Format SIRET invalide' });
        return;
      }

      // Validation du code APE
      if (code_ape && !/^[0-9]{4}[a-zA-Z]?$/.test(code_ape)) {
        res.status(400).json({ success: false, message: 'Format code APE invalide' });
        return;
      }

      // Note: La vérification de doublons est désactivée car les données sont maintenant chiffrées
      // Une future amélioration pourrait implémenter une vérification basée sur des hash
      // pour les emails et d'autres identifiants uniques

      // Préparer les données du client
      const clientData = {
        user_id: userId,
        nom,
        email,
        telephone,
        adresse,
        siret,
        tva,
        notes,
        forme_juridique,
        code_ape
      };

      // Chiffrer les données sensibles avant insertion
      const encryptedClientData = await encryptClientDataAsync(clientData);

      const { data: client, error } = await supabase
        .from('invoices_client')
        .insert([encryptedClientData])
        .select()
        .single();

      if (error) {
        logger.error('Erreur lors de la création du client:', error);
        res.status(500).json({ success: false, message: 'Erreur lors de la création du client' });
        return;
      }

      // Déchiffrer les données du client créé
      const decryptedClient = await decryptClientDataAsync(client);

      // Invalider le cache des clients pour ce user
      const listCacheKey = `${CLIENT_LIST_CACHE_PREFIX}${userId}`;
      await redis.del(listCacheKey);

      res.status(201).json({ success: true, data: decryptedClient });
    } catch (error) {
      logger.error('Erreur lors de la création du client:', error);
      res.status(500).json({ success: false, message: 'Erreur serveur' });
    }
  },

  // Mettre à jour un client
  updateClient: async (req: Request, res: Response) => {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        res.status(401).json({ success: false, message: 'Non autorisé' });
        return;
      }

      const { id } = req.params;
      if (!clientController.validateUUID(id)) {
        res.status(400).json({ success: false, message: 'ID client invalide' });
        return;
      }

      const { 
        nom, 
        email, 
        telephone, 
        adresse, 
        siret, 
        tva, 
        notes,
        forme_juridique,
        code_ape
      } = req.body;

      if (nom === '') {
        res.status(400).json({ success: false, message: 'Le nom du client est requis' });
        return;
      }

      // Validation de l'email
      if (email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
        res.status(400).json({ success: false, message: 'Format d\'email invalide' });
        return;
      }

      // Validation du SIRET (14 chiffres)
      if (siret && !/^\d{14}$/.test(siret)) {
        res.status(400).json({ success: false, message: 'Format SIRET invalide' });
        return;
      }

      // Validation du code APE
      if (code_ape && !/^[0-9]{4}[a-zA-Z]?$/.test(code_ape)) {
        res.status(400).json({ success: false, message: 'Format code APE invalide' });
        return;
      }

      // Préparer les données de mise à jour
      const updateData = {
        nom,
        email,
        telephone,
        adresse,
        siret,
        tva,
        notes,
        forme_juridique,
        code_ape
      };

      // Chiffrer les données sensibles avant mise à jour
      const encryptedUpdateData = await encryptClientDataAsync(updateData);

      const { data: client, error } = await supabase
        .from('invoices_client')
        .update(encryptedUpdateData)
        .eq('id', id)
        .eq('user_id', userId)
        .select()
        .single();

      if (error) {
        logger.error('Erreur lors de la mise à jour du client:', error);
        res.status(500).json({ success: false, message: 'Erreur lors de la mise à jour du client' });
        return;
      }

      if (!client) {
        res.status(404).json({ success: false, message: 'Client non trouvé' });
        return;
      }

      // Déchiffrer les données du client mis à jour
      const decryptedClient = await decryptClientDataAsync(client);

      // Invalider les caches
      const listCacheKey = `${CLIENT_LIST_CACHE_PREFIX}${userId}`;
      const clientCacheKey = `${CLIENT_CACHE_PREFIX}${userId}:${id}`;
      await Promise.all([
        redis.del(listCacheKey),
        redis.del(clientCacheKey)
      ]);

      res.json({ success: true, data: decryptedClient });
    } catch (error) {
      logger.error('Erreur lors de la mise à jour du client:', error);
      res.status(500).json({ success: false, message: 'Erreur serveur' });
    }
  },

  // Supprimer un client
  deleteClient: async (req: Request, res: Response) => {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        res.status(401).json({ success: false, message: 'Non autorisé' });
        return;
      }

      const { id } = req.params;
      if (!clientController.validateUUID(id)) {
        res.status(400).json({ success: false, message: 'ID client invalide' });
        return;
      }

      const { error } = await supabase
        .from('invoices_client')
        .delete()
        .eq('id', id)
        .eq('user_id', userId);

      if (error) {
        logger.error('Erreur lors de la suppression du client:', error);
        res.status(500).json({ success: false, message: 'Erreur lors de la suppression du client' });
        return;
      }

      // Invalider les caches
      const listCacheKey = `${CLIENT_LIST_CACHE_PREFIX}${userId}`;
      const clientCacheKey = `${CLIENT_CACHE_PREFIX}${userId}:${id}`;
      await Promise.all([
        redis.del(listCacheKey),
        redis.del(clientCacheKey)
      ]);

      res.json({ success: true, message: 'Client supprimé avec succès' });
    } catch (error) {
      logger.error('Erreur lors de la suppression du client:', error);
      res.status(500).json({ success: false, message: 'Erreur serveur' });
    }
  },

  // Exporter les clients en Excel
  exportClientsToExcel: async (req: Request, res: Response) => {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        res.status(401).json({ success: false, message: 'Non autorisé' });
        return;
      }

      const { data: clients, error } = await supabase
        .from('invoices_client')
        .select('*')
        .eq('user_id', userId)
        .order('nom', { ascending: true });

      if (error) {
        logger.error('Erreur lors de la récupération des clients pour export:', error);
        res.status(500).json({ success: false, message: 'Erreur lors de la récupération des clients' });
        return;
      }

      // Déchiffrer les données des clients
      const decryptedClients = await Promise.all(clients?.map(client => decryptClientDataAsync(client)) || []);

      // Préparer les données pour Excel
      const excelData = decryptedClients.map(client => ({
        'Nom': client.nom || '',
        'Email': client.email || '',
        'Téléphone': client.telephone || '',
        'Adresse': client.adresse || '',
        'SIRET': client.siret || '',
        'N° TVA': client.tva || '',
        'Forme juridique': client.forme_juridique || '',
        'Code APE': client.code_ape || '',
        'Notes': client.notes || '',
        'Date de création': client.created_at ? new Date(client.created_at).toLocaleDateString('fr-FR') : ''
      }));

      // Créer le workbook Excel
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('Clients');

      // Définir les colonnes avec leurs largeurs
      worksheet.columns = [
        { header: 'Nom', key: 'nom', width: 25 },
        { header: 'Email', key: 'email', width: 30 },
        { header: 'Téléphone', key: 'telephone', width: 15 },
        { header: 'Adresse', key: 'adresse', width: 40 },
        { header: 'SIRET', key: 'siret', width: 15 },
        { header: 'N° TVA', key: 'tva', width: 15 },
        { header: 'Forme juridique', key: 'forme_juridique', width: 20 },
        { header: 'Code APE', key: 'code_ape', width: 10 },
        { header: 'Notes', key: 'notes', width: 30 },
        { header: 'Date de création', key: 'created_at', width: 15 }
      ];

      // Ajouter les données
      excelData.forEach(client => {
        worksheet.addRow({
          nom: client['Nom'],
          email: client['Email'],
          telephone: client['Téléphone'],
          adresse: client['Adresse'],
          siret: client['SIRET'],
          tva: client['N° TVA'],
          forme_juridique: client['Forme juridique'],
          code_ape: client['Code APE'],
          notes: client['Notes'],
          created_at: client['Date de création']
        });
      });

      // Générer le buffer Excel
      const excelBuffer = await workbook.xlsx.writeBuffer();

      // Définir les headers pour le téléchargement
      const fileName = `clients_${new Date().toISOString().split('T')[0]}.xlsx`;
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);
      res.setHeader('Content-Length', Buffer.byteLength(excelBuffer));

      // Envoyer le fichier
      res.send(excelBuffer);

    } catch (error) {
      logger.error('Erreur lors de l\'export Excel des clients:', error);
      res.status(500).json({ success: false, message: 'Erreur lors de l\'export Excel' });
    }
  }
}; 