{"name": "jobconnect-platform", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --config vite.dev.config.ts", "convert:webp": "node scripts/convert-to-webp.js", "clean:images": "node scripts/clean-images.js", "build": "cross-env NODE_ENV=production vite build", "prebuild": "npm run clean:images && npm run convert:webp && tailwindcss -i ./src/index.css -o ./src/tailwind.css", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/cache": "^11.14.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fullcalendar/core": "^6.1.17", "@fullcalendar/daygrid": "^6.1.17", "@fullcalendar/interaction": "^6.1.17", "@fullcalendar/react": "^6.1.17", "@fullcalendar/timegrid": "^6.1.17", "@hookform/resolvers": "^5.0.1", "@mui/icons-material": "^7.0.2", "@mui/lab": "^7.0.0-beta.11", "@mui/material": "^7.0.2", "@mui/system": "^7.0.2", "@mui/utils": "^7.0.2", "@mui/x-date-pickers": "^8.5.0", "@tanstack/react-query": "^5.74.4", "@tiptap/extension-color": "^2.11.7", "@tiptap/extension-hard-break": "^2.11.7", "@tiptap/extension-placeholder": "^2.11.7", "@tiptap/extension-table": "^2.11.7", "@tiptap/extension-table-cell": "^2.11.7", "@tiptap/extension-table-header": "^2.11.7", "@tiptap/extension-table-row": "^2.11.7", "@tiptap/extension-text-style": "^2.11.7", "@tiptap/extension-underline": "^2.11.7", "@tiptap/react": "^2.11.7", "@tiptap/starter-kit": "^2.11.7", "@types/lodash": "^4.17.16", "axios": "^1.8.4", "browser-image-compression": "^2.0.2", "date-fns": "^4.1.0", "dompurify": "^3.2.5", "emoji-picker-react": "^4.12.2", "exceljs": "^4.4.0", "framer-motion": "^12.9.1", "ical.js": "^2.1.0", "konva": "^9.3.20", "leaflet": "^1.9.4", "lucide-react": "^0.503.0", "qrcode": "^1.5.4", "react": "^18.3.1", "react-datepicker": "^8.3.0", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.56.1", "react-hot-toast": "^2.5.2", "react-konva": "^18.0.5", "react-leaflet": "^4.2.1", "react-router-dom": "^7.5.1", "recharts": "^2.15.3", "sharp": "^0.34.1", "socket.io-client": "^4.8.1", "terser": "^5.39.0", "zod": "^3.24.3"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/preset-env": "^7.26.9", "@babel/preset-react": "^7.26.3", "@emotion/babel-plugin": "^11.13.5", "@types/babel__generator": "^7.27.0", "@types/babel__template": "^7.4.4", "@types/babel__traverse": "^7.20.7", "@types/express": "^5.0.1", "@types/jest": "^29.5.14", "@types/json-schema": "^7.0.15", "@types/leaflet": "^1.9.18", "@types/node": "^22.14.1", "@types/prop-types": "^15.7.14", "@types/qrcode": "^1.5.5", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "cross-env": "^7.0.3", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "tailwindcss": "^3.4.3", "tsx": "^4.19.3", "typescript": "^5.8.3", "vite": "^6.3.3"}}